<?php

namespace App\Http\Requests\Admin\gifts;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'orders_count'          => 'required|integer|min:1',
            'month'                 => 'required|date',
            'coupon_id'             => 'required|exists:coupons,id',
            'is_active'             => 'required|boolean',
        ];
    }
}
