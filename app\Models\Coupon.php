<?php

namespace App\Models;


class Coupon extends BaseModel
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = ['coupon_num','type','discount','max_discount','start_date','expire_date','max_use','use_times','status'];

    /**
     * Get the gifts that belong to the coupon.
     */
    public function gifts()
    {
        return $this->hasMany(Gift::class);
    }
}
