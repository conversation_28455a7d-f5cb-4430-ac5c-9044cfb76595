<?php

namespace App\Enums;

/**
 * Class OrderStatus
 *
 * @method static string all()
 * @method static string|null nameFor($value)
 * @method static array toArray()
 * @method static array forApi()
 * @method static string slug(string $value)
 */
class OrderStatus extends Base
{
    public const NEW         = 'new';
    public const ACCEPTED    = 'accepted';
    public const IN_PROGRESS = 'in_progress';
    public const DELIVERED   = 'delivered';
    public const CANCELLED   = 'cancelled';



    public static function getLabel($status)
    {
        $validStatuses = [self::NEW, self::ACCEPTED, self::IN_PROGRESS, self::DELIVERED, self::CANCELLED];
        return in_array($status, $validStatuses) ? $status : 'unknown';
    }
}

