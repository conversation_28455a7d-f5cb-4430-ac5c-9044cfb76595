<?php

namespace App\Services;

use App\Enums\OrderStatus;
use App\Http\Resources\Api\OrderResource;
use App\Http\Resources\OrderNewResource;
use App\Models\Address;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Coupon;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Notifications\OrderNotification;
use App\Notifications\NewOrderNotification;
use App\Notifications\OrderStatusUpdateNotification;
use App\Services\CartService;
use App\Services\SettingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class OrderService
{
    protected $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }
    /**
     * Get orders for the authenticated user
     *
     * @param User $user
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserOrders(User $user)
    {
        return $user->orders()->with('rating')->get();
    }

    /**
     * Get a specific order for the authenticated user
     *
     * @param User $user
     * @param int $orderId
     * @return Order
     */
    public function getUserOrder(User $user, int $orderId)
    {
        return $user->orders()->with([
            'items.product',
            'user',
            'address.city',
            'city',
            'paymentMethod',
            'deliveryPeriod',
            'rating',
        ])->findOrFail($orderId);
    }

    public function orderStats(): array
    {
        $total = Order::count();

        $newOrders = Order::where('status', OrderStatus::NEW)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get(['id', 'order_num', 'created_at', 'user_id']);

        $newCount = $newOrders->count();

        $inProgress = Order::whereIn('status', [
            OrderStatus::IN_PROGRESS,
        ])->count();

        $completed = Order::where('status', OrderStatus::DELIVERED)->count();

        return [
            'statics'    => [
                'total_orders'     => $total,
                'new_orders_count' => $newCount,
                'in_progress'      => $inProgress,
                'completed_orders' => $completed,
            ],
            'new_orders' => OrderResource::collection($newOrders),
        ];
    }

public function getOrdersByFilters(?string $status, ?string $orderNum, ?string $userName)
{
    return Order::with('user')
        ->select(['id', 'order_num', 'created_at', 'user_id', 'status'])
        ->when($status, fn($q) => $q->where('status', $status))
        ->when($orderNum, fn($q) => $q->where('order_num', 'LIKE', "%{$orderNum}%"))
        ->when($userName, function ($q) use ($userName) {
            $q->whereHas('user', fn($q2) => $q2->where('name', 'LIKE', "%{$userName}%"));
        })
        ->get();
}


    




    /**
     * Create a new order from the user's cart
     *
     * @param User $user
     * @param array $data
     * @return Order|array
     * @throws \Exception
     */
    public function createOrder(User $user, array $data)
    {
        $cart = $this->cartService->getCart($user);

        if ($cart->items->isEmpty()) {
            return [
                'status'  => 'error',
                'message' => __('apis.cart_empty'),
            ];
        }

        // Get address if provided and ensure it belongs to the user
        $address = null;
        if (! empty($data['address_id'])) {
            $address = Address::where('user_id', $user->id)->find($data['address_id']);
            if (! $address) {
                throw new \Exception(__('apis.address_not_found'));
            }
        }

        // Start transaction
        DB::beginTransaction();

        try {
            // Create the order record
            $orderResult = $this->createOrderRecord($user, $cart, $data, $address);

            // Check if there was an error with coupon validation
            if (is_array($orderResult) && isset($orderResult['status']) && $orderResult['status'] === 'error') {
                DB::rollBack();
                return $orderResult;
            }

            // If no error, $orderResult is the Order object
            $order = $orderResult;

            // Process cart items and create order items
            $this->processCartItems($order, $cart);

            // Add initial order status
            $this->addOrderStatusHistory($order, $user, OrderStatus::NEW);

            // Clear the cart after successful order creation
            $this->clearCart($cart);

            // Notify all active delivery personnel about the new order
            $this->notifyDeliveryPersonnel($order);

            DB::commit();

            return $order->load('items');
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Order creation failed', ['error' => $e->getMessage(), 'user_id' => $user->id]);
            throw new \Exception(__('apis.order_creation_failed'));
        }
    }

    /**
     * Create the order record in the database
     *
     * @param User $user
     * @param Cart $cart
     * @param array $data
     * @param Address|null $address
     * @return Order|array
     */
    private function createOrderRecord(User $user, Cart $cart, array $data, ?Address $address): Order | array
    {
        // Get VAT percentage from settings
        $settings      = SettingService::appInformations(\App\Models\SiteSetting::pluck('value', 'key'));
        $vatPercentage = (float) ($settings['vat_amount'] ?? 15);

        // Calculate VAT amount based on the setting
        $vatAmount = $cart->total_products * ($vatPercentage / 100);

        // Initialize coupon variables
        $couponAmount = 0;
        $couponId     = null;
        $couponType   = null;
        $couponValue  = null;

        // Check if coupon is provided
        if (! empty($data['coupon_num'])) {
            $couponResult = $this->applyCoupon($data['coupon_num'], $cart->total_products);

            if ($couponResult['status'] === 'success') {
                $couponAmount = $couponResult['discount_amount'];
                $couponId     = $couponResult['coupon_id'];
                $couponType   = $couponResult['coupon_type'];
                $couponValue  = $couponResult['coupon_value'];
            } else {
                // If coupon validation fails, return error data
                return [
                    'status'  => 'error',
                    'message' => $couponResult['message'],
                ];
            }
        }

        // Calculate final total with VAT and coupon discount
        $finalTotal = $cart->total_products + $vatAmount - $couponAmount;

        // Ensure final total is not negative
        if ($finalTotal < 0) {
            $finalTotal = 0;
        }

        // Prepare order data
        $orderData = [
            'user_id'            => $user->id,
            'total_products'     => $cart->total_products,
            'vat_amount'         => $vatAmount,
            'coupon_id'          => $couponId,
            'coupon_num'         => isset($data['coupon_num']) ? $data['coupon_num'] : null,
            'coupon_type'        => $couponType,
            'coupon_value'       => $couponValue,
            'coupon_amount'      => $couponAmount,
            'final_total'        => $finalTotal,
            'address_id'         => $data['address_id'],
            'payment_method_id'  => isset($data['payment_method_id']) ? $data['payment_method_id'] : null,
            'delivery_period_id' => isset($data['delivery_period_id']) ? $data['delivery_period_id'] : null,
            'city_id'            => $address ? $address->city_id : (isset($data['city_id']) ? $data['city_id'] : null),
            'lat'                => $address ? $address->latitude : (isset($data['lat']) ? $data['lat'] : null),
            'lng'                => $address ? $address->longitude : (isset($data['lng']) ? $data['lng'] : null),
            'map_desc'           => isset($data['map_desc']) ? $data['map_desc'] : null,
            'notes'              => isset($data['notes']) ? $data['notes'] : null,
        ];

        return Order::create($orderData);
    }

    /**
     * Process cart items, create order items and update product stock
     *
     * @param Order $order
     * @param Cart $cart
     * @throws \Exception
     */
    private function processCartItems(Order $order, Cart $cart): void
    {
        foreach ($cart->items as $cartItem) {
            // Process each cart item
            $this->processCartItem($order, $cartItem);
        }
    }

    /**
     * Process a single cart item
     *
     * @param Order $order
     * @param CartItem $cartItem
     * @throws \Exception
     */
    private function processCartItem(Order $order, $cartItem): void
    {
        // Lock the product row to prevent race conditions
        $product = Product::where('id', $cartItem->product_id)->lockForUpdate()->first();

        if (! $product) {
            throw new \Exception(__('apis.product_not_found'));
        }

        // Check stock availability
        if ($product->stock < $cartItem->quantity) {
            throw new \Exception(__('apis.product_out_of_stock', ['product' => $product->title]));
        }

        // Create order item
        $this->createOrderItem($order, $product, $cartItem);

        // Update product stock
        $this->updateProductStock($product, $cartItem->quantity);
    }

    /**
     * Create an order item
     *
     * @param Order $order
     * @param Product $product
     * @param CartItem $cartItem
     * @return void
     */
    private function createOrderItem(Order $order, Product $product, $cartItem): void
    {
        $order->items()->create([
            'product_id'     => $product->id,
            'quantity'       => $cartItem->quantity,
            'price'          => $cartItem->price,
            'discount_price' => $cartItem->discount_price,
            'total'          => $cartItem->total,
        ]);
    }

    /**
     * Update product stock
     *
     * @param Product $product
     * @param int $quantity
     * @return void
     */
    private function updateProductStock(Product $product, int $quantity): void
    {
        $product->update([
            'stock' => $product->stock - $quantity,
        ]);
    }

    /**
     * Add order status history
     *
     * @param Order $order
     * @param User $user
     * @param string $status
     * @return void
     */
    private function addOrderStatusHistory(Order $order, User $user, string $status): void
    {
        $order->statusHistory()->create([
            'status'          => $status,
            'statusable_id'   => $user->id,
            'statusable_type' => get_class($user),
        ]);
    }

    /**
     * Cancel an order
     *
     * @param User $user
     * @param int $orderId
     * @param string|null $reason
     * @return Order
     * @throws \Exception
     */
    public function cancelOrder(User $user, int $orderId, ?string $reason = null)
    {
        $order = $user->orders()->findOrFail($orderId);

        // Check if order can be canceled
        $this->validateOrderCanBeCanceled($order);

        // Start transaction
        DB::beginTransaction();

        try {
            // Update order status to canceled
            $this->updateOrderToCanceled($order, $reason);

            // Add order status history
            $this->addOrderStatusHistory($order, $user, OrderStatus::CANCELLED);

            // Return products to stock
            $this->returnProductsToStock($order);

            // Process refund if payment was made
            // This would be implemented based on your payment gateway integration

            DB::commit();

            return $order->load('items');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Validate that an order can be canceled
     *
     * @param Order $order
     * @throws \Exception
     */
    private function validateOrderCanBeCanceled(Order $order): void
    {
        if ($order->status != OrderStatus::NEW) {
            throw new \Exception(__('apis.order_cannot_be_canceled'));
        }
    }

    /**
     * Update order status to canceled
     *
     * @param Order $order
     * @param string|null $reason
     * @return void
     */
    private function updateOrderToCanceled(Order $order, ?string $reason): void
    {
        $order->update([
            'status' => OrderStatus::CANCELLED,
            'notes'  => $reason,
        ]);
    }

    /**
     * Return products to stock when an order is canceled
     *
     * @param Order $order
     * @return void
     */
    private function returnProductsToStock(Order $order): void
    {
        foreach ($order->items as $item) {
            $product = $item->product;
            if ($product) {
                $this->increaseProductStock($product, $item->quantity);
            }
        }
    }

    /**
     * Increase product stock
     *
     * @param Product $product
     * @param int $quantity
     * @return void
     */
    private function increaseProductStock(Product $product, int $quantity): void
    {
        $product->update([
            'stock' => $product->stock + $quantity,
        ]);
    }

    /**
     * Finish an order
     *
     * @param int $orderId
     * @return Order
     */


    /**
     * Update order status to finished
     *
     * @param Order $order
     * @return void
     */
    private function updateOrderToFinished(Order $order): void
    {
        $order->update([
            'status' => OrderStatus::DELIVERED,
        ]);
    }

    /**
     * Send notification to user about order status
     *
     * @param Order $order
     * @return void
     */
    private function sendOrderNotification(Order $order): void
    {
        $order->user->notify(new OrderNotification($order, $order->user));
    }

    /**
     * Notify all active delivery personnel about a new order
     *
     * @param Order $order
     * @return void
     */
    private function notifyDeliveryPersonnel(Order $order): void
    {
        // Get all active delivery personnel
        $deliveryPersonnel = User::where('type', 'delivery')
            ->where('active', true)
            ->where('is_blocked', false)
            ->get();

        if ($deliveryPersonnel->isNotEmpty()) {
            // Send notification to all active delivery personnel
            Notification::send($deliveryPersonnel, new NewOrderNotification($order));
        }
    }

    /**
     * Update order status (except cancel)
     *
     * @param User $user
     * @param int $orderId
     * @param string $status
     * @return Order
     * @throws \Exception
     */
    public function updateOrderStatus(User $user, int $orderId, string $status)
    {
        $order = Order::findOrFail($orderId);

        // Validate the status is not CANCEL
        $this->validateStatusNotCancel($status);

        // Start transaction
        DB::beginTransaction();

        try {
            // Update the order status
            $this->setOrderStatus($order, $status);

            // Add order status history
            $this->addOrderStatusHistory($order, $user, $status);

            // Send notification to the order's user about status change
            $order->user->notify(new OrderStatusUpdateNotification($order));

            DB::commit();

            return $order->load('items');
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Validate that the status is not CANCEL
     *
     * @param string $status
     * @throws \Exception
     */
    private function validateStatusNotCancel(string $status): void
    {
        if ($status == OrderStatus::CANCELLED) {
            throw new \Exception(__('apis.cannot_use_cancel_status'));
        }
    }

    /**
     * Set the order status
     *
     * @param Order $order
     * @param string $status
     * @return void
     */
    private function setOrderStatus(Order $order, string $status): void
    {
        $order->update([
            'status' => $status,
        ]);
    }



    /**
     * Clear the cart
     *
     * @param \App\Models\Cart $cart
     * @return void
     */
    private function clearCart($cart)
    {
        $cart->items()->delete();
        $cart->update([
            'total_qty'      => 0,
            'total_products' => 0,
            'final_total'    => 0,
        ]);
    }

    /**
     * Apply coupon to the order
     *
     * @param string $couponNum
     * @param float $totalPrice
     * @return array
     */
    private function applyCoupon(string $couponNum, float $totalPrice): array
    {
        // Find the coupon
        $coupon = Coupon::where('coupon_num', $couponNum)->first();

        if (! $coupon) {
            return [
                'status'  => 'error',
                'message' => __('apis.not_avilable_coupon'),
            ];
        }

        // Check if coupon is available
        if ($coupon->status == 'closed') {
            return [
                'status'  => 'error',
                'message' => __('apis.not_avilable_coupon'),
            ];
        }

        // Check if coupon usage limit is reached
        if ($coupon->status == 'usage_end' || $coupon->use_times >= $coupon->max_use) {
            return [
                'status'  => 'error',
                'message' => __('apis.max_usa_coupon'),
            ];
        }

        // Check if coupon is expired
        if ($coupon->expire_date < now() || $coupon->status == 'expire') {
            return [
                'status'  => 'error',
                'message' => __('apis.coupon_end_at', ['date' => $coupon->expire_date ? date('d-m-Y h:i A', strtotime($coupon->expire_date)) : '']),
            ];
        }

        // Check if coupon start date is in the future
        if ($coupon->start_date > now()) {
            return [
                'status'  => 'error',
                'message' => __('apis.coupon_start_at', ['date' => $coupon->start_date ? date('d-m-Y h:i A', strtotime($coupon->start_date)) : '']),
            ];
        }

        // Calculate discount amount
        $discountAmount = 0;

        if ($coupon->type == 'ratio') {
            // Percentage discount
            $discountAmount = ($coupon->discount * $totalPrice) / 100;

            // Check if discount exceeds max discount
            if ($discountAmount > $coupon->max_discount) {
                $discountAmount = $coupon->max_discount;
            }
        } else {
            // Fixed amount discount
            $discountAmount = $coupon->discount;

            // Ensure discount doesn't exceed total price
            if ($discountAmount > $totalPrice) {
                $discountAmount = $totalPrice;
            }
        }

        // Increment coupon usage
        $coupon->increment('use_times');

        // Check if max usage reached after increment
        if ($coupon->use_times >= $coupon->max_use) {
            $coupon->update(['status' => 'usage_end']);
        }

        return [
            'status'          => 'success',
            'message'         => __('apis.disc_amount') . ' ' . $discountAmount,
            'discount_amount' => $discountAmount,
            'coupon_id'       => $coupon->id,
            'coupon_type'     => $coupon->type,
            'coupon_value'    => $coupon->discount,
        ];
    }
}
