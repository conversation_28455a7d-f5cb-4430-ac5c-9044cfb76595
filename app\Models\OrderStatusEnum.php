<?php

namespace App\Models;

use App\Enums\OrderStatus;

class OrderStatusEnum
{
    public $key;
    public $name;

    public function __construct($key, $name)
    {
        $this->key = $key;
        $this->name = $name;
    }

    /**
     * Get all order statuses as a collection
     *
     * @return \Illuminate\Support\Collection
     */
    public static function all()
    {
        $statuses = [];
        $constants = OrderStatus::toArray();

        foreach ($constants as $key => $value) {
            $statuses[] = new self(
                $value,
                trans('order.status_' . strtolower($key))
            );
        }

        return collect($statuses);
    }
}
