<?php

namespace App\Models;

use App\Enums\OrderPayType;

class PaymentMethodEnum
{
    public $key;
    public $name;

    public function __construct($key, $name)
    {
        $this->key = $key;
        $this->name = $name;
    }

    /**
     * Get all payment methods as a collection
     *
     * @return \Illuminate\Support\Collection
     */
    public static function all()
    {
        $methods = [];
        $constants = OrderPayType::toArray();

        foreach ($constants as $key => $value) {
            $methods[] = new self(
                $value,
                trans('order.pay_type_' . strtolower($key))
            );
        }

        return collect($methods);
    }
}
