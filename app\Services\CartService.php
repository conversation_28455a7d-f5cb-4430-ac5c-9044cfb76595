<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\Product;
use App\Models\User;

class CartService
{
    /**
     * Get the user's cart, create one if it doesn't exist
     *
     * @param User $user
     * @return Cart
     */
    public function getCart(User $user)
    {
        $cart = $user->cart;

        if (!$cart) {
            $cart = $user->cart()->create([
                'total_qty' => 0,
                'total_products' => 0,
                'final_total' => 0,
            ]);
        }

        return $cart;
    }

    /**
     * Add a product to the cart
     *
     * @param User $user
     * @param array $data
     * @return Cart
     * @throws \Exception
     */
    public function addToCart(User $user, array $data)
    {
        $cart = $this->getCart($user);

        $product = Product::findOrFail($data['product_id']);

        // Check if product is active
        if (!$product->is_active) {
            throw new \Exception(__('apis.product_not_available'));
        }

        // Check if product has enough stock
        if ($product->stock < $data['quantity']) {
            throw new \Exception(__('apis.insufficient_stock'));
        }

        // Check if product already exists in cart
        $cartItem = $cart->items()->where('product_id', $product->id)->first();

        if ($cartItem) {
            // Update quantity if product already exists in cart
            $cartItem->update([
                'quantity' => $data['quantity'],
                'price' => $product->price,
                'discount_price' => $product->discount_price,
                'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
            ]);
        } else {
            // Add new product to cart
            $cart->items()->create([
                'product_id' => $product->id,
                'quantity' => $data['quantity'],
                'price' => $product->price,
                'discount_price' => $product->discount_price,
                'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
            ]);
        }

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Update a cart item
     *
     * @param User $user
     * @param array $data
     * @return Cart
     * @throws \Exception
     */
    public function updateCartItem(User $user, array $data)
    {
        $cart = $this->getCart($user);

        $cartItem = $cart->items()->findOrFail($data['cart_item_id']);
        $product = $cartItem->product;

        // Check if product is active
        if (!$product->is_active) {
            throw new \Exception(__('apis.product_not_available'));
        }

        // Check if product has enough stock
        if ($product->stock < $data['quantity']) {
            throw new \Exception(__('apis.insufficient_stock'));
        }

        // Update cart item
        $cartItem->update([
            'quantity' => $data['quantity'],
            'total' => $product->discount_price ? $product->discount_price * $data['quantity'] : $product->price * $data['quantity'],
        ]);

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Remove a product from the cart
     *
     * @param User $user
     * @param int $cartItemId
     * @return Cart
     * @throws \Exception
     */
    public function removeFromCart(User $user, int $cartItemId)
    {
        $cart = $this->getCart($user);

        $cartItem = $cart->items()->findOrFail($cartItemId);
        $cartItem->delete();

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart->load('items.product');
    }

    /**
     * Clear the cart
     *
     * @param User $user
     * @return Cart
     * @throws \Exception
     */
    public function clearCart(User $user)
    {
        $cart = $this->getCart($user);

        $cart->items()->delete();

        // Update cart totals
        $this->updateCartTotals($cart);

        return $cart;
    }



    /**
     * Update cart totals
     *
     * @param Cart $cart
     * @return void
     */
    private function updateCartTotals(Cart $cart)
    {
        // Calculate total products and quantity
        $totalProducts = $cart->items->sum('total');
        $totalQty = $cart->items->sum('quantity');

        // Calculate final total (simplified without coupons, VAT, etc.)
        $finalTotal = $totalProducts;

        // Update cart
        $cart->update([
            'total_qty' => $totalQty,
            'total_products' => $totalProducts,
            'final_total' => $finalTotal,
        ]);
    }
}
