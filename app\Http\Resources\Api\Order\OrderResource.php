<?php

namespace App\Http\Resources\Api\Order;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user' => $this->whenLoaded('user', function() {
                return new UserResource($this->user);
            }),
            'order_num' => $this->order_num,

            'address' => $this->whenLoaded('address', function() {
                return new AddressResource($this->address);
            }),

            'payment_method' => $this->whenLoaded('paymentMethod', function() {
                return new PaymentMethodResource($this->paymentMethod);
            }),
            'delivery_period' => $this->whenLoaded('deliveryPeriod', function() {
                return new DeliveryPeriodResource($this->deliveryPeriod);
            }),
            'total_qty' => (float) $this->items()->sum('quantity'),
            'total' => (float) $this->total_products,
            'coupon_amount' => (float) $this->coupon_amount,
            'vat_amount' => (float) $this->vat_amount,
            'final_total' => (float) $this->final_total,
            'status' => __('order.'.$this->status),
            'notes' => $this->notes,
            'items' => $this->whenLoaded('items', function() {
                return OrderItemResource::collection($this->items);
            }),

            'image' => $this->when(request()->routeIs('client.orders.list') || request()->routeIs('delivery.order.list'), $this->items()->first()->product->image),
            'date' => $this->created_at->format('Y-m-d H:i:s'),

        ];
    }
}
