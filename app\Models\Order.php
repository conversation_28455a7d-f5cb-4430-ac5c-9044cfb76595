<?php
namespace App\Models;

use App\Enums\OrderPayStatus;
use App\Enums\OrderPayType;
use App\Enums\OrderStatus;
use App\Traits\UploadTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use UploadTrait, HasFactory;

    protected $fillable = [
        'order_num', // auto with creating in this boot method
        'type',
        'user_id',
        'address_id',
        'city_id',
        'delivery_id',
        'delivery_period_id',
        'payment_method_id',
        'coupon_id',
        'coupon_num',
        'coupon_type',
        'coupon_value',
        'total_products',
        'coupon_amount',
        'vat_amount',
        'deliver_price',
        'final_total',
        'status',
        'pay_type',
        'pay_status',
        'pay_data',
        'lat',
        'lng',
        'map_desc',
        'notes',

    ];

    protected $casts = [
        'pay_data' => 'array',
        'lat'      => 'decimal:8',
        'lng'      => 'decimal:8',
    ];

    public function getStatusTextAttribute()
    {
        return trans('order.status_' . OrderStatus::slug($this->status));
    }

    public function getPayTypeTextAttribute()
    {
        return trans('order.pay_type_' . OrderPayType::slug($this->pay_type));
    }

    public function getPayStatusTextAttribute()
    {
        return trans('order.pay_status_' . OrderPayStatus::slug($this->pay_status));
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function address()
    {
        return $this->belongsTo(Address::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function statusHistory()
    {
        return $this->hasMany(OrderStatusHistory::class);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

    //delivery
    public function delivery()
    {
        return $this->belongsTo(User::class, 'delivery_id');
    }

    public function deliveryPeriod()
    {
        return $this->belongsTo(DeliveryPeriod::class);
    }

    public function rating()
    {
        return $this->hasOne(OrderRate::class);
    }

    public function reports()
    {
        return $this->hasMany(OrderReport::class);
    }

    public static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $lastId           = self::max('id') ?? 0;
            $model->order_num = date('Y') . ($lastId + 1);
        });
    }

    public function scopeFilterByStatus($query, $status)
    {
        if ($status !== null) {
            $query->where('status', $status);
        }

        return $query;
    }
}
