<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use DB;
use Carbon\Carbon;

class ProductCategoryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
      
            // 1
            DB::table('product_categories')->insert([
                [
                    'name'              => json_encode(['en' => 'cars'  , 'ar' => 'سيارات' ] , JSON_UNESCAPED_UNICODE) , 
                    'created_at'        => Carbon::now()
                ],[
                    'name'              => json_encode(['en' => 'Homes'  , 'ar' => 'منازل'] , JSON_UNESCAPED_UNICODE) , 
                    'created_at'        => Carbon::now()
                ],[
                    'name'              => json_encode(['en' => 'furniture'  , 'ar' => 'اثاث'] , JSON_UNESCAPED_UNICODE) , 
                    'created_at'        => Carbon::now()
                ],[
                    'name'              => json_encode(['en' => 'electronics'  , 'ar' => 'الكترونيات'] , JSON_UNESCAPED_UNICODE) , 
                    'created_at'        => Carbon::now()
                ],[
                    'name'              => json_encode(['en' => 'antiques'  , 'ar' => 'انتيكات'] , JSON_UNESCAPED_UNICODE) , 
                    'created_at'        => Carbon::now()
                ]
            ]);
    
            DB::table('product_categories')->insert([
                [
                    'name'              => json_encode(['en' => 'Ferrari'  , 'ar' => 'فيراري'], JSON_UNESCAPED_UNICODE) ,
                ] , [
                    'name'              => json_encode(['en' => 'mercedes'  , 'ar' => 'مرسيدس'], JSON_UNESCAPED_UNICODE) ,
                ] , [
                    'name'              => json_encode(['en' => 'BMW'  , 'ar' => 'BMW'], JSON_UNESCAPED_UNICODE) ,
                ] , [
                    'name'              => json_encode(['en' => 'Porsche'  , 'ar' => 'بورش'], JSON_UNESCAPED_UNICODE) ,
                ] , [
                    'name'              => json_encode(['en' => 'Land Cruiser'  , 'ar' => 'لاند كروزر'], JSON_UNESCAPED_UNICODE) ,
                ] , [
                    'name'              => json_encode(['en' => 'apartment'  , 'ar' => 'شقة'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    'name'              => json_encode(['en' => 'Villa'  , 'ar' => 'فيلا'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    'name'              => json_encode(['en' => 'architecture'  , 'ar' => 'عمارة'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    'name'              => json_encode(['en' => 'house'  , 'ar' => 'منزل'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    'name'              => json_encode(['en' => 'hotel'  , 'ar' => 'فندق'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    'name'              => json_encode(['en' => 'chair'  , 'ar' => 'كرسي'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    'name'              => json_encode(['en' => 'Bedrooms'  , 'ar' => 'غرفه نوم'], JSON_UNESCAPED_UNICODE) ,
                    
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'reception'  , 'ar' => 'ريسيبشن'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'salon'  , 'ar' => 'صالون'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'TV library'  , 'ar' => 'مكتبة تلفزيون'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'smart TV'  , 'ar' => 'شاشة سمارت'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'laptop'  , 'ar' => 'لاب توب'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'Refrigerator'  , 'ar' => 'ثلاجة'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'conditioning'  , 'ar' => 'تكييف'], JSON_UNESCAPED_UNICODE) ,
                    
                ] , [
                    
                    
                    'name'              => json_encode(['en' => 'heater'  , 'ar' => 'سخان'], JSON_UNESCAPED_UNICODE) ,
                ]
            ]);
        
    }
}
