<?php

namespace App\Models;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model implements HasMedia
{
    use HasFactory, SoftDeletes , InteractsWithMedia , HasTranslations;

    public $translatable = ['name'];

    protected $fillable = [
        'provider_id',
        'product_category_id',
        'name',
        'price',
        'quantity',
        'description',
        'is_active'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];
    public function registerMediaCollections(): void
    {

        $this->addMediaCollection('product-images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/providers/default.png'))
            ->useFallbackPath(public_path('storage/images/providers/default.png'));
    }


    public function getProductImagesUrlsAttribute()
    {
        return $this->getMedia('product-images')->map(function ($media) {
            return $media->getUrl();
        });
    }


    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    public function scopeForProvider($query, $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

}