<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProviderWorkingHour extends Model
{
    use HasFactory;

    protected $fillable = [
        'provider_id',
        'day',
        'start_time',
        'end_time',
        'is_working'
    ];

    protected $casts = [
        'is_working' => 'boolean',
    ];

    /**
     * Get the provider that owns the working hours
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Scope to get working hours for a specific day
     */
    public function scopeForDay($query, $day)
    {
        return $query->where('day', $day);
    }

    /**
     * Scope to get only working days
     */
    public function scopeWorking($query)
    {
        return $query->where('is_working', true);
    }
}
