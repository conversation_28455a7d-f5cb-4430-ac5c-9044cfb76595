<?php

namespace App\Services;

class Responder
{
    public static function success($data, $extra = [])
    {
        return response()->json(['status' => true, 'data' => $data] + $extra, 200);
    }

    public static function error($message, $errors = [], $code = 422)
    {
        return response()->json(['status' => false, 'message' => $message, 'errors' => $errors], $code);
    }


    public static function paginated($resourceCollection)
    {
        return response()->json([
            'status' => true,
            'data' => $resourceCollection->items(),
            'pagination' => [
                'current_page' => $resourceCollection->currentPage(),
                'last_page' => $resourceCollection->lastPage(),
                'per_page' => $resourceCollection->perPage(),
                'total' => $resourceCollection->total(),
                'from' => $resourceCollection->firstItem(),
                'to' => $resourceCollection->lastItem(),
            ]
        ]);
    }
}
