<?php

namespace App\Http\Controllers\Admin;

use App\Models\Client;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ClientsController extends Controller
{
    public function index($id = null) {
        if (request()->ajax()) {
            $rows = Client::search(request()->searchArray)->paginate(30);
            $html = view('admin.clientss.table', compact('rows'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.clientss.index');
    }
}
