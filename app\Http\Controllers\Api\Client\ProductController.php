<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CategoryProductsResource;
use App\Http\Resources\Api\ProductDetailResource;
use App\Http\Resources\Api\ProductResource;
use App\Services\ProductService;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * @var ProductService
     */
    protected $productService;

    /**
     * ProductController constructor.
     *
     * @param ProductService $productService
     */
    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Get all products with optional filters
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $filters = $request->only(['category_id', 'price', 'search']);
    
        // Include sort if it's an array
        if ($request->has('sort') && is_array($request->sort)) {
            $filters['sort'] = $request->sort;
        }
    
        $products = $this->productService->getAllProducts($filters);
    
        return Responder::success(
            ProductResource::collection($products)
        );
    }
    
    /**
     * Get a product with related products
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $product = $this->productService->getProductWithRelated($id);

        if (!$product) {
            return Responder::error(__('apis.product_not_found'), [], 404);
        }

        return Responder::success(
            new ProductDetailResource($product)
        );
    }

    /**
     * Get products grouped by category
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function groupedByCategory(Request $request)
    {
        $groupedProducts = $this->productService->getProductsGroupedByCategory();

        return Responder::success(
            CategoryProductsResource::collection($groupedProducts)
        );
    }
}
