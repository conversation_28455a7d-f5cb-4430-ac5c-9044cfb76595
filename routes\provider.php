<?php

use App\Http\Controllers\Api\Provider\AuthController;
use App\Http\Controllers\Api\Provider\CategoryController;
use App\Http\Controllers\Api\Provider\OrderController;
use App\Http\Controllers\Api\Provider\ServiceController;
use App\Http\Controllers\Api\Provider\SettingController;
use \App\Http\Controllers\Api\Provider\ProductController;
use App\Http\Controllers\Api\Provider\WorkingHoursController;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'Api',
    'middleware' => ['api-cors', 'api-lang'],
    'prefix'     => 'provider',
], function () {

    Route::group(['middleware' => ['guest']], function () {
        /***************************** AuthController Start *****************************/
        Route::post('register', [AuthController::class, 'register'])->name('provider.auth.register');
        Route::post('login', [AuthController::class, 'login'])->name('provider.auth.login');
        Route::post('verify', [AuthController::class, 'verify'])->name('provider.auth.verify');
        Route::post('resend-code', [AuthController::class, 'resendCode'])->name('provider.auth.resend_code');
        Route::post('forget-password', [AuthController::class, 'forgetPassword'])->name('provider.auth.forget_password');
        Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('provider.auth.reset_password');
        /***************************** AuthController End *****************************/
    });

    Route::group(['middleware' => ['auth:sanctum', 'is-active', 'is-provider' , 'is-approved-provider']], function () {
        /***************************** AuthController Start *****************************/
        Route::get('profile', [AuthController::class, 'getProfile'])->name('provider.auth.profile');
        Route::post('update-profile', [AuthController::class, 'updateProfile'])->name('provider.auth.update_profile');
        Route::patch('update-password', [AuthController::class, 'updatePassword'])->name('provider.auth.update_password');
        Route::patch('change-lang', [AuthController::class, 'changeLang'])->name('provider.auth.change_lang');
        Route::patch('switch-notify', [AuthController::class, 'switchNotificationStatus'])->name('provider.auth.switch_notify');
        Route::post('send-phone-update-code', [AuthController::class, 'sendPhoneUpdateCode'])->name('provider.auth.send_phone_update_code');
        Route::post('verify-phone-update-code', [AuthController::class, 'verifyPhoneUpdateCode'])->name('provider.auth.verify_phone_update_code');
        Route::post('logout', [AuthController::class, 'logout'])->name('provider.auth.logout');
        Route::patch('switch-orders'                          ,[AuthController::class,       'switchOrdersStatus']);
        /***************************** AuthController End *****************************/

        /***************************** ServiceController Start *****************************/
        Route::apiResource('services', ServiceController::class);
        Route::post('services/{id}/toggle-status', [ServiceController::class, 'toggleStatus'])->name('provider.services.toggle_status');
        Route::get('services-active', [ServiceController::class, 'active'])->name('provider.services.active');
        Route::get('services-stats', [ServiceController::class, 'stats'])->name('provider.services.stats');
        /***************************** ServiceController End *****************************/


        /***************************** ProductController Start *****************************/
        Route::apiResource('products', ProductController::class);
        Route::post('products/{id}/toggle-status', [ProductController::class, 'toggleStatus']);
        Route::get('products-stats', [ProductController::class, 'stats']);

        /***************************** ProductController End *****************************/

        /***************************** WorkingHoursController Start *****************************/
        Route::get('working-hours', [WorkingHoursController::class, 'index'])->name('provider.working_hours.index');
        Route::post('working-hours', [WorkingHoursController::class, 'store'])->name('provider.working_hours.store');
        /***************************** WorkingHoursController End *****************************/

    });
});