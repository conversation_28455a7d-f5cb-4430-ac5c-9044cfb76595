<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GiftUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'gift_id',
        'user_id',
    ];

    /**
     * Get the gift that owns the gift user.
     */
    public function gift()
    {
        return $this->belongsTo(Gift::class);
    }

    /**
     * Get the user that owns the gift user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
