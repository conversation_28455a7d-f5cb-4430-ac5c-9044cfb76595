<?php

namespace App\Http\Controllers\Api\Provider;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Provider\StoreServiceRequest;
use App\Http\Requests\Api\Provider\UpdateServiceRequest;
use App\Http\Resources\Api\ServiceResource;
use App\Models\Service as ServiceModel;
use App\Services\ServiceService;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    protected $serviceService;

    public function __construct(ServiceService $serviceService)
    {
        $this->serviceService = $serviceService;
    }

    /**
     * Display a listing of the provider's services
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        // Get filters from request
        $filters = $request->only([
            'is_active', 'category_id', 'search',
            'min_price', 'max_price'
        ]);

        $perPage = $request->get('per_page', 15);

        $services = $this->serviceService->getProviderServices($providerId, $filters, $perPage);

        return Responder::paginated(ServiceResource::collection($services));
    }

    /**
     * Store a newly created service
     */
    public function store(StoreServiceRequest $request)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $this->serviceService->createService($providerId, $request->validated());
        return Responder::success([], ['message' => __('apis.service_created_successfully')]);
    }

    /**
     * Display the specified service
     */
    public function show($id)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $service = $this->serviceService->getServiceById($id, $providerId);

        if (!$service) {
            return Responder::error(__('apis.service_not_found'), [], 404);
        }

        return Responder::success(new ServiceResource($service));
    }

    /**
     * Update the specified service
     */
    public function update(UpdateServiceRequest $request, $id)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $service = ServiceModel::find($id);

        if (!$service) {
            return Responder::error(__('apis.service_not_found'), [], 404);
        }

        if (!$this->serviceService->providerOwnsService($service, $providerId)) {
            return Responder::error(__('apis.unauthorized_access'), [], 403);
        }

        $service = $this->serviceService->updateService($service, $request->validated());

        // Load relationships for the resource
        $service->load(['category']);

        return Responder::success([], ['message' => __('apis.service_updated_successfully')]);
    }

    /**
     * Remove the specified service
     */
    public function destroy($id)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $service = ServiceModel::find($id);

        if (!$service) {
            return Responder::error(__('apis.service_not_found'), [], 404);
        }

        if (!$this->serviceService->providerOwnsService($service, $providerId)) {
            return Responder::error(__('apis.unauthorized_access'), [], 403);
        }

        $this->serviceService->deleteService($service);

        return Responder::success([], ['message' => __('apis.service_deleted_successfully')]);
    }

    /**
     * Toggle service active status
     */
    public function toggleStatus($id)
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $service = ServiceModel::find($id);

        if (!$service) {
            return Responder::error(__('apis.service_not_found'), [], 404);
        }

        if (!$this->serviceService->providerOwnsService($service, $providerId)) {
            return Responder::error(__('apis.unauthorized_access'), [], 403);
        }

        $service = $this->serviceService->toggleServiceStatus($service);

        // Load relationships for the resource
        $service->load(['category']);

        $message = $service->is_active
            ? __('apis.service_activated_successfully')
            : __('apis.service_deactivated_successfully');

        return Responder::success([], ['message' => $message]);
    }

    /**
     * Get active services for the provider
     */
    public function active()
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $services = $this->serviceService->getActiveServices($providerId);

        return Responder::success(ServiceResource::collection($services));
    }

    /**
     * Get services statistics for the provider
     */
    public function stats()
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $stats = $this->serviceService->getProviderServicesStats($providerId);

        return Responder::success($stats);
    }
}
