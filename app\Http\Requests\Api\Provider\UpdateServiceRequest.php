<?php

namespace App\Http\Requests\Api\Provider;

use App\Http\Requests\Api\BaseApiRequest;

class UpdateServiceRequest extends BaseApiRequest
{
    public function rules()
    {
        return [
            'name'                    => 'required|array',
            'name.ar'                 => 'required|string|max:100',
            'name.en'                 => 'nullable|string|max:100',
            'description'             => 'required|array',
            'description.ar'          => 'required|string|max:100',
            'description.en'          => 'nullable|string|max:100',            'price' => 'sometimes|required|numeric|min:0|max:999999.99',
            'duration' => 'sometimes|required|integer|min:1|max:1440', // max 24 hours in minutes
            'expected_time_to_accept' => 'nullable|integer|min:1|max:1440',
            'is_active' => 'sometimes|boolean',
            'category_id' => 'sometimes|required|exists:categories,id',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'The service name is required.',
            'name.max' => 'The service name may not be greater than 255 characters.',
            'price.required' => 'The service price is required.',
            'price.numeric' => 'The service price must be a number.',
            'price.min' => 'The service price must be at least 0.',
            'price.max' => 'The service price may not be greater than 999999.99.',
            'duration.required' => 'The service duration is required.',
            'duration.integer' => 'The service duration must be an integer.',
            'duration.min' => 'The service duration must be at least 1 minute.',
            'duration.max' => 'The service duration may not be greater than 1440 minutes (24 hours).',
            'expected_time_to_accept.integer' => 'The expected time to accept must be an integer.',
            'expected_time_to_accept.min' => 'The expected time to accept must be at least 1 minute.',
            'expected_time_to_accept.max' => 'The expected time to accept may not be greater than 1440 minutes (24 hours).',
            'description.max' => 'The description may not be greater than 1000 characters.',
            'category_id.required' => 'The category is required.',
            'category_id.exists' => 'The selected category is invalid.',
        ];
    }
}
