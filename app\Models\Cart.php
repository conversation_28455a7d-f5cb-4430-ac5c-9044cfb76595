<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $fillable = [
        'user_id',
        'total_qty',
        'total_products',
        'final_total',
    ];

    protected $casts = [
        'total_qty' => 'integer',
        'total_products' => 'decimal:2',
        'final_total' => 'decimal:2',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(CartItem::class);
    }


}
