<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Gift extends BaseModel
{
    use HasFactory;

    protected $fillable = [
        'orders_count',
        'month',
        'coupon_id',
        'is_active',
    ];

    protected $casts = [
        'month' => 'date',
        'orders_count' => 'integer',
        'coupon_id' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the users that belong to the gift.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'gift_users')->withTimestamps();
    }

    /**
     * Get the coupon that belongs to the gift.
     */
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }
}
