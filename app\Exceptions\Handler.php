<?php

namespace App\Exceptions;

use App\Facades\Responder;
use App\Traits\ResponseTrait;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler {
  use ResponseTrait;

  protected $dontReport = [
    //
  ];

  protected $dontFlash = [
    'password',
    'password_confirmation',
  ];

  public function report(Throwable $exception) {
    parent::report($exception);
  }

  public function render($request, Throwable $exception) {
    if ($request->is('api/*') || $request->expectsJson()) {
      // Handle validation exceptions
      if ($exception instanceof ValidationException) {
        return Responder::error(
          Arr::first(Arr::first($exception->errors())),
          $exception->errors()
        );
      }

      if ($exception instanceof ModelNotFoundException) {
        $msg = __('apis.model_not_found');
      }

      if ($exception instanceof NotFoundHttpException) {
        $msg = __('apis.route_not_found');
      }

      if ($exception instanceof AuthenticationException) {
        return $this->unauthenticatedReturn();
      }

      return $this->response('exception', $msg ?? $exception->getMessage(),
        ['line' => $exception->getLine(), 'file' => $exception->getFile()]
      );
    }

    return parent::render($request, $exception);
  }
}
