<footer>
    <div class="container">
        <div>
            <div class="row">
                <div class="col-md-4">
                    <a href="" class="mb-20">
                        <img  loading="lazy"  src="<?php echo e($settings['intro_logo']); ?>" class="logo_footer">
                    </a>
                    <ul class="social-m d-flex">
                        <?php $__currentLoopData = $socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><a target="_blank" href="<?php echo e($social->url); ?>"><i class="<?php echo e($social->icon); ?>"></i></a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                <div class="col-md-4">
                    <div>
                        <h6 class="Tfooter"><?php echo e(__('intro_site.quiek_links')); ?></h6>
                        <ul class="link-footer">
                            <li><i class="fas fa-angle-double-left"></i> <a href="#home" data-scroll="home"><?php echo e(__('intro_site.home')); ?></a></li>
                            <li><i class="fas fa-angle-double-left"></i> <a href="#who_we" data-scroll="who_we"><?php echo e(__('intro_site.who_us')); ?></a></li>
                            <li><i class="fas fa-angle-double-left"></i> <a href="#our_service" data-scroll="our_service"><?php echo e(__('intro_site.our_services')); ?></a></li>
                            <li><i class="fas fa-angle-double-left"></i> <a href="#how_work" data-scroll="how_work"><?php echo e(__('intro_site.who_we_work')); ?></a></li>
                            <li><i class="fas fa-angle-double-left"></i> <a href="#FAQ" data-scroll="FAQ"><?php echo e(__('intro_site.faq')); ?></a></li>
                            <li><i class="fas fa-angle-double-left"></i> <a href="#connect_us" data-scroll="connect_us"><?php echo e(__('intro_site.contact_us')); ?></a></li>

                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6 class="Tfooter"><?php echo e(__('intro_site.contact_us')); ?></h6>
                    <ul class="link-footer">
                        <li><i class="fas fa-map-marker-alt"></i> <a><?php echo e($settings['intro_address']); ?></a></li>
                        <li><i class="fas fa-phone"></i> <a href="tel:<?php echo e($settings['intro_phone']); ?>"><?php echo e($settings['intro_phone']); ?></a></li>
                        <li><i class="far fa-envelope"></i> <a href="mailto: <?php echo e($settings['intro_email']); ?>"><?php echo e($settings['intro_email']); ?></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="under_footer">
        <div class="row">
            <div class="col-md-8">
                <?php echo e(__('intro_site.recives')); ?> <?php echo e(\Carbon\Carbon::now()->year); ?> <?php echo e(__('intro_site.site')); ?> <a href=""><?php echo e($settings['intro_name_'.lang()]); ?></a>
            </div>
            <div class="col">
                <a href="<?php echo e(route('IntroPrivacyPolicy')); ?>"><?php echo e(__('intro_site.privacy')); ?></a>
            </div>
        </div>
    </div>
</footer>

  
    <!--========================== Start Loading Page ======================-->

    <div class="loader">
        <img  loading="lazy"  src="<?php echo e($settings['intro_loader']); ?>" alt="">
    </div>

    <!--========================= End Loading Page =========================-->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <?php echo $settings['google_analytics']; ?>

    <script src="<?php echo e(asset('intro_site/js/jquery-3.2.1.min.js')); ?>"></script>
    <script src="<?php echo e(asset('intro_site/js/popper.min.js')); ?>"></script>
    <script src="<?php echo e(asset('intro_site/js/bootstrap.min.js')); ?>"></script>
    <!-- plugins JS -->
    <script src="<?php echo e(asset('intro_site/plugins/owl-carousel/js/owl.carousel.min.js')); ?>"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js" integrity="sha512-VEd+nq25CkR676O+pLBnDW09R7VQX9Mdiij052gVCp5yVH3jGtH70Ho/UUv4mJDsEdTvqRCFZg0NKGiojGnUCw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- my JS -->
    <script src="<?php echo e(asset('intro_site/js/main.js')); ?>"></script>

    <script> 
        $(document).on('submit','.send-message',function (e) {
            e.preventDefault()
            var url = $(this).attr('action')
            $.ajax({
                url: url,
                method: 'post',
                data: new FormData($(this)[0]),
                dataType:'json',
                processData: false,
                contentType: false,
                success: function(response){
                    $('.error_meassages').remove();
                        toastr.success(response.message)
                        $('.send-message')[0].reset()
                },
                error: function (xhr) {
                    $('.error_meassages').remove();
                    $.each(xhr.responseJSON.errors, function(key,value) {
                        $('.send-message input[name=' + key + ']').after('<small class="form-text error_meassages text-danger">' + value + '</small>');
                        $('.send-message textarea[name=' + key + ']').after('<small class="form-text error_meassages text-danger">' + value + '</small>');
                    });
                },
            });
        })
    </script>

</body>

</html><?php /**PATH D:\Workstation\sorriso-backend\resources\views/intro_site/layouts/footer.blade.php ENDPATH**/ ?>