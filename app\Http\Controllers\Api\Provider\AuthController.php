<?php

namespace App\Http\Controllers\Api\Provider;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\Provider\LoginRequest;
use App\Http\Requests\Api\Auth\Provider\RegisterRequest;
use App\Http\Resources\Api\ProviderResource;
use App\Models\User;
use App\Models\UserUpdate;
use App\Services\AuthService;
use App\Traits\GeneralTrait;
use App\Traits\ResponseTrait;
use App\Traits\SmsTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    use ResponseTrait, SmsTrait, GeneralTrait;

    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(RegisterRequest $request)
    {
        // Prepare user data
        $userData = $request->safe()->only([
            'name', 'country_code', 'phone', 'email', 'password',
            'id_number', 'city_id'
        ]);
        $userData['type'] = 'provider';

        // Prepare provider data
        $providerData = $request->safe()->only([
            'commercial_name', 'salon_type', 'residence_type',
            'commercial_register_no', 'sponsor_name', 'sponsor_phone',
            'institution_name', 'lat', 'lng'
        ]);

        // Prepare bank data
        $bankData = $request->safe()->only([
            'holder_name', 'bank_name', 'account_number', 'iban'
        ]);
        $bankData['is_default'] = true;

        // Prepare media files
        $mediaFiles = [
            'commercial_register_image' => $request->file('commercial_register_image'),
            'logo' => $request->file('logo'),
            'residence_image' => $request->file('residence_image')
        ];

        // Register provider using service
        $user = $this->authService->registerProvider(
            $userData,
            $providerData,
            $bankData,
            $mediaFiles
        );

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        // Return response
        $userData = new ProviderResource($user);
        return Responder::success(['user' => $userData], ['message' => __('auth.registered')]);
    }

    public function login(LoginRequest $request)
    {
        $user = User::where('phone', $request->phone)
            ->where('type', 'provider')
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return Responder::error(__('auth.failed'));
        }

        if (!$user->status == 'active') {
            $data = $user->sendVerificationCode();
            return Responder::error(__('auth.not_active'), $data, 203);
        }

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        // Check if user is not approved by admin
        if ($user->provider && $user->provider->status == 'pending') {
            return Responder::error(__('auth.not_approved'), [], 403);
        }

        return Responder::success(['user' => $user->login()], ['message' => __('apis.signed')]);
    }

    public function getProfile(Request $request)
    {
        $user = auth()->user();

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ProviderResource::make($user)->setToken($requestToken);

        return Responder::success($userData);
    }

    public function updateProfile(\App\Http\Requests\Api\Auth\Provider\UpdateProfileRequest $request)
    {
        $user = auth()->user();
        $data = $request->validated();

        // Prepare user data
        $userData = $request->safe()->only([
            'name', 'email', 'city_id'
        ]);

        // Prepare provider data
        $providerData = $request->safe()->only([
            'lat', 'lng', 'commercial_register_no', 'sponsor_name',
            'sponsor_phone', 'institution_name'
        ]);

        // Prepare bank data
        $bankData = $request->safe()->only([
            'holder_name', 'bank_name', 'account_number', 'iban'
        ]);

        // Prepare media files
        $mediaFiles = [];

        if ($request->hasFile('logo')) {
            $mediaFiles['logo'] = $request->file('logo');
        }

        if ($request->hasFile('salon_images')) {
            $mediaFiles['salon_images'] = $request->file('salon_images');
        }


        // Update provider profile using service
        $user = $this->authService->updateProviderProfile(
            $user,
            $userData,
            $providerData,
            $bankData,
            $mediaFiles
        );

        // Load relationships for the resource
        $user->load(['provider.bankAccount', 'city']);

        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ProviderResource::make($user->refresh())->setToken($requestToken);

        if (!$user->active) {
            $data = $user->sendVerificationCode();
            return Responder::error(__('auth.not_active'), $data, 203);
        }

        return Responder::success(['user' => $userData], ['message' => __('apis.updated')]);
    }

    /**
     * Send verification code to new phone number
     */
  
}


