<?php
namespace App\Http\Requests\Api\Auth\Provider;

use App\Http\Requests\Api\BaseApiRequest;
use Illuminate\Http\Request;

class RegisterRequest extends BaseApiRequest
{

    public function __construct(Request $request)
    {
        $request['phone']        = fixPhone($request['phone']);
        $request['country_code'] = fixPhone($request['country_code']);
    }

    public function rules()
    {
        return [
            // User fields
            'name'                      => 'required|max:50',
            'country_code'              => 'required|numeric|digits_between:2,5',
            'phone'                     => 'required|numeric|digits_between:8,10|unique:users,phone,NULL,id,deleted_at,NULL',
            'email'                     => 'required|email|unique:users,email,NULL,id,deleted_at,NULL|max:50',
            'password'                  => 'required|min:6|max:100|confirmed',
            'city_id'                   => 'required|exists:cities,id',

            // Provider fields
            'commercial_name'           => 'required|array',
            'commercial_name.ar'        => 'required|string|max:100',
            'commercial_name.en'        => 'nullable|string|max:100',
            'salon_type'                => 'required|string|in:salon,beauty_center',
            'residence_type'            => 'required_if:nationality,other|string|in:individual,professional',
            'nationality'               => 'required|string|in:saudi,other',
            'logo'                      => 'required|image',
            'lat'                       => 'required|numeric',
            'lng'                       => 'required|numeric',
            'commercial_register_no'    => 'required|string|max:50',
            'commercial_register_image' => 'required|image',
            'sponsor_name'              => 'required_if:nationality,other|string|max:100',
            'sponsor_phone'             => 'required_if:nationality,other|string|max:20',
            'institution_name'          => 'required|string|max:100',
            'residence_image'           => 'required_if:nationality,other|image',

            // Bank account fields
            'holder_name'               => 'required|string|max:100',
            'bank_name'                 => 'required|string|max:100',
            'account_number'            => 'required|string|max:50',
            'iban'                      => 'required|string|max:50',
        ];
    }
}
