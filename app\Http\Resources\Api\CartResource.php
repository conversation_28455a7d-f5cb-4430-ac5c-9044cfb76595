<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'total_qty' => $this->total_qty,
            'total_products' => $this->total_products,
            'final_total' => $this->final_total,
            'items' => CartItemResource::collection($this->whenLoaded('items')),
        ];
    }
}
