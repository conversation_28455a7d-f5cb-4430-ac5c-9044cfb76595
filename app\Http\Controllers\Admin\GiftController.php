<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\gifts\Store;
use App\Http\Requests\Admin\gifts\Update;
use App\Models\Gift;
use App\Models\Coupon;
use App\Traits\Report;


class GiftController extends Controller
{
    public function index($id = null)
    {
        if (request()->ajax()) {
            $gifts = Gift::search(request()->searchArray)->paginate(30);
            $html = view('admin.gifts.table' ,compact('gifts'))->render() ;
            return response()->json(['html' => $html]);
        }
        return view('admin.gifts.index');
    }

    public function create()
    {
        $coupons = Coupon::where('status', 'available')->get();
        return view('admin.gifts.create', compact('coupons'));
    }


    public function store(Store $request)
    {
        Gift::create($request->only(['orders_count', 'month', 'coupon_id', 'is_active']));

        Report::addToLog('  اضافه هدية') ;
        return response()->json(['url' => route('admin.gifts.index')]);
    }
    public function edit($id)
    {
        $gift = Gift::with(['coupon'])->findOrFail($id);
        $coupons = Coupon::where('status', 'available')->get();
        return view('admin.gifts.edit', compact('gift', 'coupons'));
    }

    public function update(Update $request, $id)
    {
        $gift = Gift::findOrFail($id);
        $gift->update($request->only(['orders_count', 'month', 'coupon_id', 'is_active']));

        Report::addToLog('  تعديل هدية') ;
        return response()->json(['url' => route('admin.gifts.index')]);
    }

    public function show($id)
    {
        $gift = Gift::with(['coupon'])->findOrFail($id);
        return view('admin.gifts.show', compact('gift'));
    }
    public function destroy($id)
    {
        Gift::findOrFail($id)->delete();
        Report::addToLog('  حذف هدية') ;
        return response()->json(['id' =>$id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (Gift::whereIntegerInRaw('id',$ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من الهدايا') ;
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
