<?php

namespace Database\Seeders;

use App\Models\Adv;
use Faker\Provider\Color;
use Illuminate\Database\Seeder;
use Database\Factories\AdsFactory;
use Faker\Factory as FakerFactory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AdsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
       // Create a new instance of the Faker generator
       $faker = FakerFactory::create();

       // Add the Color provider to the Faker instance
       $faker->addProvider(new Color($faker));

       // Change the values according to your needs
       $numberOfUsers = 10; // Number of users to generate

       for ($i = 0; $i < $numberOfUsers; $i++) {
           Adv::create([
               'advertiser_class' => $faker->randomElement(['marketer', 'advertiser']),
               'advertiser_number' => $faker->phoneNumber,
               'desc' => $faker->text,
               'views' => $faker->numberBetween(0, 1000),
               'ad_name' => 'sdwqe',
               'category_id' => $faker->numberBetween(1, 10),
               'width' => $faker->randomFloat(2, 1, 10),
               'length' => $faker->randomFloat(2, 1, 10),
               'price' => $faker->randomFloat(2, 100, 1000),
               'destination' => $faker->address,
               'latitude' => $faker->latitude,
               'longitude' => $faker->longitude,
               'area' => $faker->numberBetween(100, 1000),
               'client_id' => 1, // Assuming clients table has the 'id' column
               'land_desc' => $faker->text,
               'street_width' => $faker->randomFloat(2, 5, 20),
               'land_address' => $faker->address,
               'refreshed_at' => $faker->dateTimeThisYear,
               'last_update' => $faker->dateTimeThisYear,
               'color_hex' => $faker->hexColor, // Generate a random color hex
               'status' => $faker->randomElement(['pending', 'active']), // Add the status field

           ]);


    }
}
}
