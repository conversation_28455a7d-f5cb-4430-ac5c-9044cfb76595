<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderItemFactory extends Factory
{
    public function definition(): array
    {
        $product = Product::inRandomOrder()->first() ?? Product::factory()->create();
        $quantity = $this->faker->numberBetween(1, 5);
        $price = $product->price ?? $this->faker->randomFloat(2, 50, 200);
        $hasDiscount = $this->faker->boolean(50);
        $discountPrice = $hasDiscount ? $this->faker->randomFloat(2, 10, $price) : null;
        $finalPrice = $discountPrice ?? $price;

        return [
            'order_id'       => Order::inRandomOrder()->first()?->id ?? Order::factory(),
            'product_id'     => $product->id,
            'quantity'       => $quantity,
            'price'          => $price,
            'discount_price' => $discountPrice,
            'total'          => $finalPrice * $quantity,
        ];
    }
}
