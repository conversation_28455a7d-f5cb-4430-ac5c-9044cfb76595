<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    public function definition(): array
    {
        $title = [
            'en' => $this->faker->words(3, true),
            'ar' => 'منتج ' . $this->faker->numberBetween(1, 100),
        ];

        $description = [
            'en' => $this->faker->sentence(),
            'ar' => 'وصف المنتج: ' . $this->faker->sentence(),
        ];

        $price = $this->faker->randomFloat(2, 50, 300);
        $hasDiscount = $this->faker->boolean(40);
        $discountPrice = $hasDiscount ? $this->faker->randomFloat(2, 10, $price) : null;

        return [
            'title'          => $title,
            'description'    => $description,
            'price'          => $price,
            'discount_price' => $discountPrice,
            'stock'          => $this->faker->numberBetween(0, 100),
            'image'          => $this->faker->imageUrl(640, 480, 'products', true),
            'is_active'      => $this->faker->boolean(80),
            'category_id'    => Category::inRandomOrder()->first()?->id ?? Category::factory(),
        ];
    }
}
