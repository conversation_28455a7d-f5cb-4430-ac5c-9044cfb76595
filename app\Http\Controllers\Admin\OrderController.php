<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\orders\Store;
use App\Http\Requests\Admin\orders\Update;
use App\Models\Order;
use App\Traits\Report;

class OrderController extends Controller
{
    public function index($id = null)
    {


        //Undefined variable $orders
            $orders = Order::latest()->get();




        if (request()->ajax()) {
            $orders = Order::search(request()->searchArray)->paginate(30);
            $html = view('admin.orders.table', compact('orders'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.orders.index',Compact('orders'));
    }

    public function create()
    {
        return view('admin.orders.create');
    }

    public function store(Store $request)
    {
        $order = Order::create($request->validated());

        // Log success
        Report::addToLog('اضافة منتج');

        return response()->json(['url' => route('admin.orders.index')]);
    }

    public function edit($id)
    {
        $order = Order::findOrFail($id);
        return view('admin.orders.edit', ['order' => $order]);
    }

    public function update(Update $request, $id)
    {
        $order = Order::findOrFail($id);
        $order->update($request->validated());

        // Log success
        Report::addToLog('تعديل منتج');

        return response()->json(['url' => route('admin.orders.index')]);
    }

    public function show($id)
    {
        $order = Order::findOrFail($id);
        return view('admin.orders.show', ['order' => $order]);
    }

    public function destroy($id)
    {
        Order::findOrFail($id)->delete();
        Report::addToLog('حذف منتج');
        return response()->json(['id' => $id]);
    }

    public function destroyAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        $ids = collect($requestIds)->pluck('id')->toArray();

        if (Order::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('حذف العديد من المنتجات');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
