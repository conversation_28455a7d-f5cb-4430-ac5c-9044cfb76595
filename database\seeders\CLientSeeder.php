<?php

namespace Database\Seeders;

use App\Models\Client;
use Illuminate\Database\Seeder;
use Faker\Factory as FakerFactory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class CLientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = FakerFactory::create();


            Client::create([
                'name' => $faker->name,
                'email' => $faker->email,
                'phone' => $faker->phoneNumber,
                'password' => Hash::make('password'), // You can change the default password if needed
                'city_id' => 1, // Adjust the range according to your city data
                'ads_count' => 0,
                'available_advs' => 5,
                'lat' => $faker->latitude,
                'lang' => $faker->longitude,

            ]);
        }
    }

