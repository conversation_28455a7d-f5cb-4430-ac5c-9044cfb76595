<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddressRequest extends FormRequest
{
    public function authorize()
    {
        return true;  
    }

    public function rules()
    {
        return [
            'full_name'       => 'required|string|max:255',
            'city_id'         => 'required|exists:cities,id',
            'neighborhood'    => 'required|string|max:255',
            'type'            => 'required|in:home,work,mosque,school',
            'address'         => 'required|string|max:500',
            'building_number' => 'required|string|max:50',
            'floor_number'    => 'required|string|max:50',
            'latitude'        => 'required|string|max:255',   
            'longitude'       => 'required|string|max:255',   
            'phone'           => 'required|string|max:20',
            'notes'           => 'required|string',
            'is_default'      => 'boolean',
        ];
    }
}
