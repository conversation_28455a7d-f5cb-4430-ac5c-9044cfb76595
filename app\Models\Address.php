<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Address extends Model
{
        use HasFactory;  

    protected $fillable = [
        'full_name',
        'city_id',
        'user_id',
        'neighborhood',
        'type',
        'address',
        'building_number',
        'floor_number',
        'latitude',
        'longitude',
        'phone',
        'notes',
        'is_default'
    ];

    protected $casts = [
        'is_default' => 'boolean',
    ];

    public function city()
    {
        return $this->belongsTo(City::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
