<?php

namespace App\Http\Controllers\Api\Client;

use App\Facades\Responder;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\Client\LoginRequest;
use App\Http\Requests\Api\Auth\Client\RegisterRequest;
use App\Http\Resources\Api\ClientResource;
use App\Models\User;
use App\Traits\GeneralTrait;
use App\Traits\ResponseTrait;
use App\Traits\SmsTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    use ResponseTrait, SmsTrait, GeneralTrait;

    public function register(RegisterRequest $request)
    {
        $userData = $request->validated();
        $userData['type'] = 'client';
        $userData['active'] = false; // Requires activation
        
        // Create the user first
        $user = User::create($userData);
        
        // Handle image upload if provided
        if ($request->hasFile('image')) {
            $user->addMedia($request->file('image'))
                ->toMediaCollection('profile');
        }
        
        $user->sendVerificationCode();
        
        $userData = new ClientResource($user->refresh());
        return Responder::success(['user' => $userData], ['message' => __('auth.registered')]);
    }

    public function login(LoginRequest $request)
    {
        if (!$user = User::where('phone', $request['phone'])
            ->where('country_code', $request['country_code'])
            ->where('type', 'client')
            ->first()) {
            return Responder::error(__('auth.failed'));
        }

        if (!Hash::check($request->password, $user->password)) {
            return Responder::error(__('auth.failed'));
        }

        if ($user->status == 'blocked') {
            $user->logout();
            return Responder::error(__('auth.blocked'), [], 423);
        }

        if ($user->status == 'pending') {
            $data = $user->sendVerificationCode();
            return Responder::error(__('auth.not_active'), $data, 203);
        }

        // Check if user account is deleted (soft deleted)
        if ($user->trashed()) {
            return Responder::error(__('auth.account_deleted'), [], 410);
        }

       
      
        return Responder::success(['user' => $user->login()], ['message' => __('apis.signed')]);
    }

    public function getProfile(Request $request)
    {
        $user = auth()->user();
        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ClientResource::make($user)->setToken($requestToken);
        
        return Responder::success($userData);
    }

    public function updateProfile(\App\Http\Requests\Api\Auth\Client\UpdateProfileRequest $request)
    {
        $user = auth()->user();
        $data = $request->validated();
        
        // Handle image separately
        if ($request->hasFile('image')) {
            $user->clearMediaCollection('profile');
            $user->addMedia($request->file('image'))
                ->toMediaCollection('profile');
        }
        
        $user->update($data);
        
        $requestToken = ltrim($request->header('authorization'), 'Bearer ');
        $userData = ClientResource::make($user->refresh())->setToken($requestToken);
        
        if (!$user->active) {
            $data = $user->sendVerificationCode();
            return Responder::error(__('auth.not_active'), $data, 203);
        }
        
        return Responder::success(['user' => $userData], ['message' => __('apis.updated')]);
    }
}


