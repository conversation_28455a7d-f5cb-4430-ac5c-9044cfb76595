<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContactUsRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->check();
    }

    public function rules()
    {
        return [
            'type' => 'required|in:suggestion,complaint',
            'title' => 'required|string',
            'body' => 'required|string|max:1000',
        ];
    }
}
