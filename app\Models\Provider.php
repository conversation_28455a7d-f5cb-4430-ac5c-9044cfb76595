<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Provider extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, HasTranslations;

    public $translatable = ['commercial_name'];

    protected $fillable = [
        'user_id',
        'provider_type',
        'salon_type',
        'commercial_name',
        'commercial_register_no',
        'institution_name',
        'sponsor_name',
        'sponsor_phone',
        'is_mobile',
        'mobile_service_fee',
        'description',
        'status',
        'accept_orders',
        'wallet_balance',
        'withdrawable_balance',
        'nationality',
        'lat',
        'lng',
        'map_desc',
        'nationality',
        'residence_type'
    ];

    protected $casts = [
        'is_mobile' => 'boolean',
        'accept_orders' => 'boolean',
        'wallet_balance' => 'decimal:2',
        'withdrawable_balance' => 'decimal:2',
        'lat' => 'decimal:8',
        'lng' => 'decimal:8',
    ];

    /**
     * Register media collections for the provider
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/providers/default.png'))
            ->useFallbackPath(public_path('storage/images/providers/default.png'));

        $this->addMediaCollection('commercial_register_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/providers/default.png'))
            ->useFallbackPath(public_path('storage/images/providers/default.png'));

        $this->addMediaCollection('residence_image')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/providers/default.png'))
            ->useFallbackPath(public_path('storage/images/providers/default.png'));

        $this->addMediaCollection('salon_images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/providers/default.png'))
            ->useFallbackPath(public_path('storage/images/providers/default.png'));
    }

    /**
     * Register media conversions for the provider
     */
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(100)
            ->height(100)
            ->nonQueued();
    }

    /**
     * Get the logo URL attribute
     */
    public function getLogoUrlAttribute()
    {
        return $this->getFirstMediaUrl('logo');
    }

    /**
     * Get the residence image URL attribute
     */
    public function getResidenceImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('residence_image');
    }

    public function getCommercialImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('commercial_register_image');
    }

    /**
     * Get the salon images URLs attribute
     */
    public function getSalonImagesUrlsAttribute()
    {
        return $this->getMedia('salon_images')->map(function ($media) {
            return $media->getUrl();
        });
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bankAccount()
    {
        return $this->hasOne(ProviderBankAccount::class);
    }

    /**
     * Get the services for the provider
     */
    public function services()
    {
        return $this->hasMany(Service::class);
    }

    /**
     * Get active services for the provider
     */
    public function activeServices()
    {
        return $this->hasMany(Service::class)->where('is_active', true);
    }

    /**
     * Get the working hours for the provider
     */
    public function workingHours()
    {
        return $this->hasMany(ProviderWorkingHour::class);
    }
}


