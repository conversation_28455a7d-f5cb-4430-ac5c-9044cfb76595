<?php

namespace App\Http\Middleware;

use Closure;

class WebCors {
  public function handle($request, Closure $next) {
    $response = $next($request);
    $response->headers->set('Access-Control-Allow-Origin' , '*');
    $response->headers->set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE');
    $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Accept, Authorization, X-Requested-With, Application');
    return $response;
  }
}
