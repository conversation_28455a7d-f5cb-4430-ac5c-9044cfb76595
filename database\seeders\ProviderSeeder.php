<?php

namespace Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create('ar_SA');
        $providers = [];
        
        for ($i = 0; $i < 10; $i++) {
            $providers[] = [
                'name' => $faker->company,
                'phone' => "52222222$i",
                'email' => $faker->unique()->companyEmail,
                'password' => bcrypt(123456),
                'is_blocked' => rand(0, 1),
                'active' => rand(0, 1),
                'email_verified_at' => now(),
                'remember_token' => Str::random(10),
                'avatar' => null,
                'lat' => $faker->latitude,
                'lng' => $faker->longitude,
                'address' => $faker->address,
                'commercial_record' => Str::random(10),
                'tax_number' => Str::random(15),
                'service_type' => Arr::random(['maintenance', 'delivery', 'cleaning']),
                'lang' => 'ar',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        DB::table('providers')->insert($providers);
    }
}