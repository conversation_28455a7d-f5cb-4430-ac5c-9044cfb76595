<?php

namespace App\Http\Requests\Api\Order;

use App\Http\Requests\Api\BaseApiRequest;

class CreateOrderRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'address_id' => 'required|exists:addresses,id',
            'map_desc' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'coupon_num' => 'nullable|string|exists:coupons,coupon_num',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'delivery_period_id' => 'required|exists:delivery_periods,id',
        ];
    }
}
