<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'name' => [
                    'en' => 'Visa',
                    'ar' => 'فيزا'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Mada',
                    'ar' => 'مدى'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Apple Pay',
                    'ar' => 'آبل باي'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Google Pay',
                    'ar' => 'جوجل باي'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Tabby',
                    'ar' => 'تابي'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Tamara',
                    'ar' => 'تمارا'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
            [
                'name' => [
                    'en' => 'Cash on Delivery',
                    'ar' => 'الدفع عند الاستلام'
                ],
                'image' => 'storage/images/default.png',
                'is_active' => true
            ],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::create($method);
        }
    }
}
