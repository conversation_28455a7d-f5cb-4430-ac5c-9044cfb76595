{"name": "server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js"}, "author": "", "license": "ISC", "dependencies": {"config": "^1.30.0", "date-and-time": "^3.0.3", "dotenv": "^10.0.0", "express": "^4.16.3", "fcm-node": "^1.6.1", "i18n-nodejs": "^3.0.0", "lodash": "^4.17.4", "moment": "^2.22.2", "mysql": "^2.18.1", "mysql2": "^3.6.2", "nodemon": "^2.0.12", "parser": "^0.1.4", "socket.io": "^4.2.0", "utf8": "^3.0.0"}}