<?php

namespace App\Http\Requests\Api\Order;

use App\Enums\OrderStatus;
use App\Http\Requests\Api\BaseApiRequest;

class UpdateOrderStatusRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Check if status is valid and not CANCELLED
                    if (!in_array($value, [
                        OrderStatus::ACCEPTED,
                        OrderStatus::IN_PROGRESS,
                        OrderStatus::DELIVERED
                    ])) {
                        $fail(__('apis.invalid_order_status'));
                    }

                    // Ensure status is not CANCELLED
                    if ($value == OrderStatus::CANCELLED) {
                        $fail(__('apis.cannot_use_cancel_status'));
                    }
                },
            ],
        ];
    }
}
