<?php
namespace App\Http\Controllers\Api\Client;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Order\CancelOrderRequest;
use App\Http\Requests\Api\Order\CreateOrderRequest;
use App\Http\Resources\Api\Order\OrderResource;
use App\Services\OrderService;
use App\Services\Responder;
use App\Traits\ResponseTrait;

class OrderController extends Controller
{
    use ResponseTrait;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * OrderController constructor.
     *
     * @param OrderService $orderService
     */
    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Get user orders
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrders()
    {
        $user   = auth()->user();
        $orders = $this->orderService->getUserOrders($user);

        return Responder::success(['orders' => OrderResource::collection($orders)]);
    }

    /**
     * Get order details
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOrder($id)
    {
        $user  = auth()->user();
        $order = $this->orderService->getUserOrder($user, $id);

        return Responder::success(['order' => new OrderResource($order)]);
    }

    /**
     * Create a new order from the cart
     *
     * @param \App\Http\Requests\Api\Order\CreateOrderRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOrder(CreateOrderRequest $request)
    {
        try {
            $user   = auth()->user();
            $result = $this->orderService->createOrder($user, $request->validated());

            // Check if the result is an error response from coupon validation
            if (is_array($result) && isset($result['status']) && $result['status'] === 'error') {
                return Responder::error($result['message'], [], 422);
            }

            // If not an error, $result is the Order object
            return Responder::success(null, ['message' => __('apis.order_created')]);
        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 422);
        }
    }

    /**
     * Cancel an order
     *
     * @param \App\Http\Requests\Api\Order\CancelOrderRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelOrder(CancelOrderRequest $request)
    {
        try {
            $user      = auth()->user();
            $validated = $request->validated();
            $order     = $this->orderService->cancelOrder($user, $validated['order_id'], $validated['reason'] ?? null);

            return Responder::success( ['message' => __('apis.order_canceled')]);
        } catch (\Exception $e) {
            return Responder::error($e->getMessage(), [], 422);
        }
    }

   
}
