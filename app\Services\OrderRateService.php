<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderRate;
use App\Models\User;
use App\Enums\OrderStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderRateService
{
    /**
     * Rate an order
     *
     * @param User $user
     * @param array $data
     * @return OrderRate
     * @throws \Exception
     */
    public function rateOrder(User $user, array $data): OrderRate
    {
        DB::beginTransaction();

        try {
            // Get the order
            $order = Order::findOrFail($data['order_id']);

            // Validate that the order belongs to the user
            $this->validateOrderOwnership($order, $user);

            // Validate that the order can be rated
            $this->validateOrderCanBeRated($order);

            // Check if order is already rated
            $this->validateOrderNotAlreadyRated($order);

            // Create the rating
            $rating = OrderRate::create([
                'order_id' => $order->id,
                'rate' => $data['rate'],
                'user_id' => $user->id,
                'note' => $data['note'] ?? null,
            ]);

            DB::commit();

          
            return $rating->load('order');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order rating failed', [
                'error' => $e->getMessage(),
                'order_id' => $data['order_id'] ?? null,
                'user_id' => $user->id
            ]);
            throw $e;
        }
    }

  
    /**
     * Validate that the order belongs to the user
     *
     * @param Order $order
     * @param User $user
     * @throws \Exception
     */
    private function validateOrderOwnership(Order $order, User $user): void
    {
        if ($order->user_id !== $user->id) {
            throw new \Exception(__('apis.order_not_belongs_to_user'));
        }
    }

    /**
     * Validate that the order can be rated (must be delivered)
     *
     * @param Order $order
     * @throws \Exception
     */
    private function validateOrderCanBeRated(Order $order): void
    {
        if ($order->status !== OrderStatus::DELIVERED) {
            throw new \Exception(__('apis.order_must_be_delivered_to_rate'));
        }
    }

    /**
     * Validate that the order is not already rated
     *
     * @param Order $order
     * @throws \Exception
     */
    private function validateOrderNotAlreadyRated(Order $order): void
    {
        if ($order->rating) {
            throw new \Exception(__('apis.order_already_rated'));
        }
    }
}
