<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'rate',
        'note',
    ];

    protected $casts = [
        'rate' => 'decimal:2',
    ];

    /**
     * Get the order that owns the rating
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
