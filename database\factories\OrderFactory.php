<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use App\Models\Address;
use App\Models\Coupon;
use App\Models\DeliveryPeriod;
use App\Models\PaymentMethod;
use App\Enums\OrderStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class OrderFactory extends Factory
{
    protected $model = Order::class;

public function definition(): array
{
    return [
        'order_num'            => $this->faker->unique()->numberBetween(1, 99999),
        'type'                 => $this->faker->numberBetween(0, 3),
        'user_id'              => User::inRandomOrder()->first()->id ?? 1,
        'delivery_id'          => User::inRandomOrder()->first()->id ?? null,
        'address_id'           => Address::inRandomOrder()->first()->id ?? null,
        'coupon_id'            => Coupon::inRandomOrder()->first()->id ?? null,
        'city_id'              => $this->faker->numberBetween(1, 10),
        'delivery_period_id'   => DeliveryPeriod::inRandomOrder()->first()->id ?? null,
        'payment_method_id'    => PaymentMethod::inRandomOrder()->first()->id ?? null,
        'coupon_num'           => (string) Str::uuid(),
        'coupon_type'          => $this->faker->randomElement(['fixed', 'percent']),
        'coupon_value'         => $this->faker->randomNumber(2),
        'total_products'       => $this->faker->randomFloat(2, 50, 500),
        'coupon_amount'        => $this->faker->randomFloat(2, 0, 50),
        'deliver_price'        => $this->faker->randomFloat(2, 10, 40),
        'final_total'          => $this->faker->randomFloat(2, 100, 1000),
        'status'               => $this->faker->randomElement([
                                    OrderStatus::NEW,
                                    OrderStatus::ACCEPTED,
                                    OrderStatus::IN_PROGRESS,
                                    OrderStatus::DELIVERED,
                                    OrderStatus::CANCELLED
                                ]),
        'pay_status'           => $this->faker->numberBetween(0, 1),
        'pay_data'             => json_encode([
                                    'gateway' => 'mada',
                                    'transaction_id' => 'TX-' . Str::uuid(),
                                ]),
        'lat'                  => $this->faker->latitude(),
        'lng'                  => $this->faker->longitude(),
        'map_desc'             => $this->faker->address(),
        'notes'                => $this->faker->sentence(),
        'created_at'           => now(),
        'updated_at'           => now(),
    ];
}

}
