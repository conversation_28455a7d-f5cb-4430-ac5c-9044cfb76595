<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\City;
use App\Models\User;

class AddressFactory extends Factory
{
    public function definition()
    {
        return [
            'full_name'       => $this->faker->name(),
            'city_id'         => City::inRandomOrder()->first()?->id,
            'user_id'         => User::inRandomOrder()->first()?->id,
            'neighborhood'    => $this->faker->streetName(),
            'type'            => $this->faker->randomElement(['home', 'work', 'mosque', 'school']),
            'address'         => $this->faker->address(),
            'building_number' => $this->faker->buildingNumber(),
            'floor_number'    => $this->faker->numberBetween(1, 10),
            'latitude'        => $this->faker->latitude(),
            'longitude'       =>$this->faker->longitude(),
            'phone'           => $this->faker->phoneNumber(),
            'notes'           => $this->faker->sentence(),
            'is_default'      => $this->faker->boolean(20), 
        ];
    }
}
