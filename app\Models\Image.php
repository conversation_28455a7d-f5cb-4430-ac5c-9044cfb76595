<?php

namespace App\Models;


class Image extends BaseModel
{
    protected const IMAGEPATH = 'images';
    protected $fillable = ['start_date','end_date','link','is_active','image'];

    /**
     * Get the image URL with a fallback to default image
     *
     * @return string
     */
    public function getImageUrlAttribute()
    {
        if (isset($this->attributes['image']) && $this->attributes['image'] && $this->attributes['image'] != 'default.png') {
            return $this->getImage($this->attributes['image'], self::IMAGEPATH);
        }

        return $this->defaultImage(self::IMAGEPATH);
    }
}
