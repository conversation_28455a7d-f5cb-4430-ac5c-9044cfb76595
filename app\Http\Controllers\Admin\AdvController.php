<?php

namespace App\Http\Controllers\Admin;

use App\Builders\Input;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\advs\Store;
use App\Http\Requests\Admin\advs\Update;
use App\Models\Adv;
use App\Traits\Report;
use Illuminate\Http\Request;

class AdvController extends Controller {
    private function inputs($options = null) {
        return [
            'image'       => Input::imageInput()->build(),
            'title'        => Input::createArEnInput(__('admin.title_ar'), __('admin.name_en'))->build(),
            'description' => Input::createArEnTextarea(__('admin.description_ar'), __('admin.description_en'))->build(),
            // 'user_id'     => Input::selectInput(__('admin.users'), $options['users'], 'id', 'name')->build(),
            // 'images'      => Input::filesInput(__('admin.images'), $options['images'] ?? [], 'image')
            //     ->deleteRoute(route('admin.advs.deleteImage'))
            //     ->attribute('accept', 'image/png, image/jpg, image/jpeg')
            //     ->build(),
            // 'sizes'       => Input::customInput()->build(),
            // 'anyName'     => Input::seoInputs()->build(), // add (meta_title , meta_description , meta_keywords) inputs in ar ,en
        ];
    }

    public function index($id = null) {
        if (request()->ajax()) {
            $advs = Adv::search(request()->searchArray)->paginate(30);
            $html  = view('admin.advs.table', compact('advs'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.advs.index');
    }

    public function create() {
        // $users = User::get();
        return view('admin.advs.create', ['inputs' => $this->inputs([
            // 'users' => $users
        ])]);
    }

    public function store(Store $request) {
        Adv::create($request->validated());
        Report::addToLog('اضافه اعلان');
        return response()->json(['url' => route('admin.advs.index')]);
    }
    public function edit($id) {
        $adv = Adv::findOrFail($id);
        // $files = $adv->files;
        // $users = User::get();
        return view('admin.advs.edit', ['item' => $adv, 'inputs' => $this->inputs([
            // 'users' => $users,
            // 'files' => $files
        ])]);
    }

    public function update(Update $request, $id) {
        $adv = Adv::findOrFail($id)->update($request->validated());
        Report::addToLog('  تعديل اعلان');
        return response()->json(['url' => route('admin.advs.index')]);
    }

    public function show($id) {
        $adv = Adv::findOrFail($id);
        // $files = $adv->files;
        // $users = User::get();
        return view('admin.advs.show', ['item' => $adv, 'inputs' => $this->inputs([
            // 'users' => $users,
            // 'files' => $files
        ])]);
    }
    public function destroy($id) {
        $adv = Adv::findOrFail($id)->delete();
        Report::addToLog('  حذف اعلان');
        return response()->json(['id' => $id]);
    }

    public function destroyAll(Request $request) {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if (Adv::whereIntegerInRaw('id', $ids)->get()->each->delete()) {
            Report::addToLog('  حذف العديد من اعلانات');
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }
}
