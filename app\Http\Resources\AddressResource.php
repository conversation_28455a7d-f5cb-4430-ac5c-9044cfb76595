<?php
namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    protected $isIndex;

    public function __construct($resource, $isIndex = false)
    {
        parent::__construct($resource);
        $this->isIndex = $isIndex;
    }

    public function toArray($request)
    {
        if ($this->isIndex) {
            return [
                'id' => $this->id,
                'full_name' => $this->full_name,
                'phone' => $this->phone,
                'building_number' => $this->building_number,
                'neighborhood' => $this->neighborhood,
                'city' => $this->city ? $this->city->getTranslation('name', 'ar') : null,
                'floor_number' => $this->floor_number,
            ];
        }

        return [
            'id' => $this->id,
            'full_name' => $this->full_name,
            'phone' => $this->phone,
            'building_number' => $this->building_number,
            'neighborhood' => $this->neighborhood,
            'city' => $this->city ? $this->city->getTranslation('name', 'ar') : null,
            'floor_number' => $this->floor_number,
            'type' => $this->type,
            'address' => $this->address,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'notes' => $this->notes,
            'is_default' => $this->is_default,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
