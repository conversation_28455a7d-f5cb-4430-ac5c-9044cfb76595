<?php

namespace App\Http\Requests\Api\Auth\Provider;

use App\Http\Requests\Api\BaseApiRequest;
use Illuminate\Http\Request;

class UpdateProfileRequest extends BaseApiRequest {

  public function __construct(Request $request) {
    if (isset($request['phone'])) {
      $request['phone'] = fixPhone($request['phone']);
    }
    if (isset($request['country_code'])) {
      $request['country_code'] = fixPhone($request['country_code']);
    }
  }

  public function rules() {
    return [
      // User fields
      'name'                      => 'sometimes|max:50',
'email' => 'sometimes|email|max:50|unique:users,email,' . auth()->id() . ',id,deleted_at,NULL',
      'city_id'                   => 'sometimes|exists:cities,id',

      // Provider fields
      'logo'                      => 'sometimes|nullable|image',
      'salon_images'              => 'sometimes|nullable|array|max:5',
      'salon_images.*'            => 'image',
      'lat'                       => 'sometimes|nullable|numeric',
      'lng'                       => 'sometimes|nullable|numeric',
      'commercial_register_no'    => 'sometimes|nullable|string|max:50',
      'sponsor_name'              => 'sometimes|nullable|string|max:100',
      'sponsor_phone'             => 'sometimes|nullable|string|max:20',
      'institution_name'          => 'sometimes|nullable|string|max:100',
      // Note: commercial_register_image and residence_image are not updatable during profile updates

      // Bank account fields
      'holder_name'               => 'sometimes|nullable|string|max:100',
      'bank_name'                 => 'sometimes|nullable|string|max:100',
      'account_number'            => 'sometimes|nullable|string|max:50',
      'iban'                      => 'sometimes|nullable|string|max:50',
    ];
  }
}