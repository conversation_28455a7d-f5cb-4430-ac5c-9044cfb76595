<?php

namespace App\Models;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class Category extends Model implements HasMedia
{
    use HasTranslations , InteractsWithMedia;


    protected $fillable = ['name','is_active'];
    public $translatable = ['name'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('categories')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/users/default.png'))
            ->useFallbackPath(public_path('storage/images/users/default.png'));
    }
    
    /**
     * Get the services for the category
     */
    public function services()
    {
        return $this->hasMany(\App\Models\Service::class);
    }

    public function getServicesCountAttribute()
    {
        return $this->services()->count();
    }
}
