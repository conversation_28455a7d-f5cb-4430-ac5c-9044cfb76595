<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'note',
    ];

    /**
     * Get the user that owns the report
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order that is being reported
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
