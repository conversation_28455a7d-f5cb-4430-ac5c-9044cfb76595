<?php

namespace App\Services;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;

class CategoryService
{

    public function __construct(protected Category $model)
    {
    }
    /**
     * Get all active categories
     *
     * @param int|null $parentId
     * @return Collection
     */
    public function getAllCategories(): Collection
    {
        return  $this->model->where('is_active', 1)->get();
    }

    /**
     * Get a category with its products
     *
     * @param int $id
     * @return Category|null
     */
    public function getCategoryWithProducts(int $id): ?Category
    {
        return $this->model->where('id', $id)
            ->where('is_active', 1)
            ->with(['products' => function($query) {
                $query->where('is_active', 1)
                      ->latest();
            }])
            ->first();
    }
}