<?php

namespace App\Http\Requests\Api\Provider;

use App\Http\Requests\Api\BaseApiRequest;

class StoreWorkingHoursRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'working_hours' => 'required|array|min:1|max:7',
            'working_hours.*.day' => 'required|string|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            'working_hours.*.start_time' => 'required|date_format:H:i',
            'working_hours.*.end_time' => 'required|date_format:H:i|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'working_hours.required' => 'Working hours are required.',
            'working_hours.array' => 'Working hours must be an array.',
            'working_hours.min' => 'At least one working day must be specified.',
            'working_hours.max' => 'Maximum 7 working days can be specified.',
            'working_hours.*.day.required' => 'Day is required for each working hour.',
            'working_hours.*.day.in' => 'Day must be one of: sunday, monday, tuesday, wednesday, thursday, friday, saturday.',
            'working_hours.*.start_time.required' => 'Start time is required for each working hour.',
            'working_hours.*.start_time.date_format' => 'Start time must be in HH:MM format.',
            'working_hours.*.end_time.required' => 'End time is required for each working hour.',
            'working_hours.*.end_time.date_format' => 'End time must be in HH:MM format.',
            'working_hours.*.end_time.after' => 'End time must be after start time.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set default value for is_working if not provided
        if ($this->has('working_hours')) {
            $workingHours = $this->input('working_hours');
            foreach ($workingHours as $index => $workingHour) {
                if (!isset($workingHour['is_working'])) {
                    $workingHours[$index]['is_working'] = true;
                }
            }
            $this->merge(['working_hours' => $workingHours]);
        }
    }
}
