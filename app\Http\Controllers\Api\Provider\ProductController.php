<?php

namespace App\Http\Controllers\Api\Provider;

use App\Http\Requests\Api\Provider\UpdateProudctRequest;
use Illuminate\Http\Request;
use App\Services\ProductService;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Provider\StoreProudctRequest;
use \App\Http\Resources\Api\Provider\ProductResource;
use App\Models\Product;
use App\Services\Responder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class ProductController extends Controller
{
    public function __construct(private ProductService $productService)
    {
    }

    public function index(Request $request): JsonResponse
    {
        $perPage = $request->input('per_page', 10);
        $providerId = auth()->user()->provider->id;
        $products = $this->productService->getProviderProducts($providerId , $perPage);
        return Responder::paginated(ProductResource::collection($products));
    }



    public function show(int $id): JsonResponse
    {
        $product = $this->productService->getProductById($id);

        if (!$product) {
            return Responder::error('Product not found', [], 404);
        }

        return Responder::success(new ProductResource($product));
    }

    public function store(StoreProudctRequest $request): JsonResponse
    {
        $product = $this->productService->createProduct($request->validated());
        return Responder::success(new ProductResource($product), ['message' => 'Product created successfully']);
    }

    public function update(UpdateProudctRequest $request, int $id): JsonResponse
    {
        $product = $this->productService->getProductById($id);

        if (!$product) {
            return Responder::error('Product not found', [], 404);
        }

        $this->productService->updateProduct($product, $request->validated());
        return Responder::success(new ProductResource($product->refresh()), ['message' => 'Product updated successfully']);
    }

    public function destroy(int $id): JsonResponse
    {
        $product = $this->productService->getProductById($id);

        if (!$product) {
            return Responder::error('Product not found', [], 404);
        }

        $this->productService->deleteProduct($product);
        return Responder::success(null, ['message' => 'Product deleted successfully']);
    }

    public function restore(int $id): JsonResponse
    {
        $product = Product::withTrashed()->find($id);

        if (!$product) {
            return Responder::error('Product not found', [], 404);
        }

        $this->productService->restoreProduct($id);
        return Responder::success(null, ['message' => 'Product restored successfully']);
    }

    public function toggleStatus(int $id): JsonResponse
    {
        $product = $this->productService->getProductById($id);

        if (!$product) {
            return Responder::error('Product not found', [], 404);
        }

        $this->productService->toggleProductStatus($product);
        return Responder::success([
            'is_active' => $product->refresh()->is_active
        ], ['message' => 'Product status updated']);
    }

    public function stats()
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $stats = $this->productService->getProviderProductsStats($providerId);

        return Responder::success($stats);
    }
}
