<?php

namespace App\Http\Requests\Admin\products;

use Illuminate\Foundation\Http\FormRequest;

class Store extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $default_lang = config('app.locale');

        return [
            'title'                 => 'required|array',
            "title.{$default_lang}"  => 'required|string|max:191',
            'title.*'               => 'nullable|string|max:191',
            'description'           => 'required|array',
            "description.{$default_lang}" => 'required|string',
            'description.*'         => 'nullable|string',
            'price'                 => 'required|numeric|min:0',
            'discount_price'        => 'nullable|numeric|min:0',
            'stock'                 => 'required|integer|min:0',
            'image'                 => 'nullable|image',
            'is_active'             => 'nullable|boolean',
            'category_id'           => 'required|exists:categories,id',
        ];
    }
}
