<?php

namespace App\Models;

use Spa<PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class ProductCategory extends Model implements HasMedia
{
    use HasTranslations , InteractsWithMedia;

    protected $fillable = ['name','is_active'];
    public $translatable = ['name'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product-categories')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/users/default.png'))
            ->useFallbackPath(public_path('storage/images/users/default.png'));
    }
    
}
