<?php

namespace App\Services;

use App\Models\Address;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AddressService
{
    public function __construct(
        private readonly Address $address
    ) {}

    public function index(): Collection
    {
        return $this->address
            ->whereUserId(Auth::id())
            ->latest()
            ->get();
    }

    public function store(array $data): Address
    {
        try {
            DB::beginTransaction();
            
            // If this address is set as default, unset other default addresses
            if (($data['is_default'] ?? false) === true) {
                $this->unsetDefaultAddresses();
            }
            
            $address = $this->address->create([
                ...$data,
                'user_id' => Auth::id()
            ]);
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function show(string $id): Address|false|null
    {
        $address = $this->address->find($id);
        
        if (!$address) {
            return null;
        }
        
        return $address->user_id === Auth::id() 
            ? $address 
            : false;
    }

    public function update(array $data, string $id): Address|false|null
    {
        try {
            DB::beginTransaction();
            
            $address = $this->address->find($id);
            
            if (!$address) {
                return null;
            }
            
            if ($address->user_id !== Auth::id()) {
                return false;
            }
            
            // If this address is being set as default, unset other default addresses
            if (($data['is_default'] ?? false) === true) {
                $this->unsetDefaultAddresses();
            }
            
            $address->update($data);
            
            DB::commit();
            return $address->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function destroy(string $id): bool|null
    {
        try {
            DB::beginTransaction();
            
            $address = $this->address->find($id);
            
            if (!$address) {
                return null;
            }
            
            if ($address->user_id !== Auth::id()) {
                return false;
            }
            
            $address->delete();
            
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function unsetDefaultAddresses(): void
    {
        $this->address
            ->whereUserId(Auth::id())
            ->where('is_default', true)
            ->update(['is_default' => false]);
    }
}