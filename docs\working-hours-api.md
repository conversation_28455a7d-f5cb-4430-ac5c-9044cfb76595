# Provider Working Hours API

This API allows providers to manage their working hours by storing them as an array containing day, start time, and end time.

## Endpoints

### 1. Get Working Hours
**GET** `/api/provider/working-hours`

Returns the current working hours for the authenticated provider.

**Response:**
```json
{
    "key": "success",
    "msg": "Success",
    "data": [
        {
            "id": 1,
            "day": "monday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true,
            "created_at": "2025-01-27T10:00:00.000000Z",
            "updated_at": "2025-01-27T10:00:00.000000Z"
        },
        {
            "id": 2,
            "day": "tuesday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true,
            "created_at": "2025-01-27T10:00:00.000000Z",
            "updated_at": "2025-01-27T10:00:00.000000Z"
        }
    ]
}
```

### 2. Store/Update Working Hours
**POST** `/api/provider/working-hours`

Stores or updates working hours for the authenticated provider. If working hours for a specific day already exist, they will be updated. If not, new records will be created.

**Request Body:**
```json
{
    "working_hours": [
        {
            "day": "monday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true
        },
        {
            "day": "tuesday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true
        },
        {
            "day": "wednesday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true
        },
        {
            "day": "thursday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true
        },
        {
            "day": "friday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true
        },
        {
            "day": "saturday",
            "start_time": "10:00",
            "end_time": "14:00",
            "is_working": true
        },
        {
            "day": "sunday",
            "start_time": "00:00",
            "end_time": "00:00",
            "is_working": false
        }
    ]
}
```

**Validation Rules:**
- `working_hours`: Required array with minimum 1 and maximum 7 items
- `working_hours.*.day`: Required string, must be one of: sunday, monday, tuesday, wednesday, thursday, friday, saturday
- `working_hours.*.start_time`: Required time in HH:MM format
- `working_hours.*.end_time`: Required time in HH:MM format, must be after start_time
- `working_hours.*.is_working`: Optional boolean, defaults to true

**Response:**
```json
{
    "key": "success",
    "msg": "Working hours saved successfully",
    "data": [
        {
            "id": 1,
            "day": "monday",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_working": true,
            "created_at": "2025-01-27T10:00:00.000000Z",
            "updated_at": "2025-01-27T10:00:00.000000Z"
        }
        // ... other days
    ]
}
```

## Authentication

Both endpoints require authentication using Sanctum token and the following middleware:
- `auth:sanctum`
- `is-active`
- `is-provider`
- `is-approved-provider`

## Database Structure

The working hours are stored in the `provider_working_hours` table with the following structure:
- `id`: Primary key
- `provider_id`: Foreign key to providers table
- `day`: Enum (sunday, monday, tuesday, wednesday, thursday, friday, saturday)
- `start_time`: Time field
- `end_time`: Time field
- `is_working`: Boolean field
- `timestamps`: Created at and updated at

## Usage Notes

1. The working hours are stored as individual records for each day, allowing for flexible scheduling
2. When updating working hours, existing records for each day are updated if they exist, or new records are created if they don't exist
3. The `is_working` field allows providers to mark certain days as non-working days
4. Time format should be in 24-hour format (HH:MM)
5. End time must be after start time for the same day
6. Each day can be updated independently - you don't need to send all 7 days in each request
