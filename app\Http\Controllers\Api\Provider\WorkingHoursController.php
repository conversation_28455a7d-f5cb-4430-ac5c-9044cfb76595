<?php

namespace App\Http\Controllers\Api\Provider;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Provider\StoreWorkingHoursRequest;
use App\Http\Resources\Api\Provider\WorkingHoursResource;
use App\Services\WorkingHoursService;
use App\Services\Responder;
use Illuminate\Http\JsonResponse;

class WorkingHoursController extends Controller
{
    protected $workingHoursService;

    public function __construct(WorkingHoursService $workingHoursService)
    {
        $this->workingHoursService = $workingHoursService;
    }

    /**
     * Display a listing of the provider's working hours
     */
    public function index(): JsonResponse
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $workingHours = $this->workingHoursService->getProviderWorkingHours($providerId);

        return Responder::success(WorkingHoursResource::collection($workingHours));
    }

    /**
     * Store working hours for the provider
     */
    public function store(StoreWorkingHoursRequest $request): JsonResponse
    {
        $user = auth()->user();
        $providerId = $user->provider->id;

        $workingHours = $this->workingHoursService->storeWorkingHours($providerId, $request->validated());

        return Responder::success(
            WorkingHoursResource::collection($workingHours),
            ['message' => __('apis.working_hours_saved_successfully')]
        );
    }
}
