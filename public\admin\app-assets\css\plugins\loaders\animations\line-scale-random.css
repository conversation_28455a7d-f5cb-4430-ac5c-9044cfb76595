/*========================================================
        DARK LAYOUT
=========================================================*/
@-webkit-keyframes line-scale-party {
  0% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
  50% {
    -webkit-transform : scale(0.5);
            transform : scale(0.5);
  }
  100% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
}
@keyframes line-scale-party {
  0% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
  50% {
    -webkit-transform : scale(0.5);
            transform : scale(0.5);
  }
  100% {
    -webkit-transform : scale(1);
            transform : scale(1);
  }
}

.line-scale-party > div:nth-child(1) {
  -webkit-animation-delay : 0.07s;
          animation-delay : 0.07s;
  -webkit-animation-duration : 0.75s;
          animation-duration : 0.75s;
}

.line-scale-party > div:nth-child(2) {
  -webkit-animation-delay : -0.03s;
          animation-delay : -0.03s;
  -webkit-animation-duration : 0.98s;
          animation-duration : 0.98s;
}

.line-scale-party > div:nth-child(3) {
  -webkit-animation-delay : -0.16s;
          animation-delay : -0.16s;
  -webkit-animation-duration : 0.72s;
          animation-duration : 0.72s;
}

.line-scale-party > div:nth-child(4) {
  -webkit-animation-delay : 0.22s;
          animation-delay : 0.22s;
  -webkit-animation-duration : 0.43s;
          animation-duration : 0.43s;
}

.line-scale-party > div {
  background-color : #B8C2CC;
  width : 4px;
  height : 3.45rem;
  border-radius : 2px;
  margin : 2px;
  -webkit-animation-fill-mode : both;
          animation-fill-mode : both;
  display : inline-block;
  -webkit-animation-name : line-scale-party;
          animation-name : line-scale-party;
  -webkit-animation-iteration-count : infinite;
          animation-iteration-count : infinite;
  -webkit-animation-delay : 0;
          animation-delay : 0;
}