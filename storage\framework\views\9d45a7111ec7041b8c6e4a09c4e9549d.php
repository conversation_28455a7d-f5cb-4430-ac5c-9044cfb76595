<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="rtl">
<!-- BEGIN: Head-->

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="Vuexy admin is super flexible, powerful, clean &amp; modern responsive bootstrap 4 admin template with unlimited possibilities.">
    <meta name="keywords" content="admin template, Vuexy admin template, dashboard template, flat admin template, responsive admin template, web app">
    <meta name="author" content="PIXINVENT">
    <title><?php echo $__env->yieldContent('title',isset(\Request::route()->getAction()['title']) ? __('admin.'.\Request::route()->getAction()['title']) : ''); ?></title>
    <link rel="apple-touch-icon" href="<?php echo e($settings['fav_icon']); ?>">
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo e($settings['fav_icon']); ?>">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/switchery/0.8.2/switchery.min.css" integrity="sha512-uyGg6dZr3cE1PxtKOCGqKGTiZybe5iSq3LsqOolABqAWlIRLo/HKyrMMD8drX+gls3twJdpYX0gDKEdtf2dpmw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">

    <?php if(in_array(lang(),json_decode($settings['rtl_locales']))): ?>
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/vendors-rtl.min.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/bootstrap.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/bootstrap-extended.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/colors.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/components.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/themes/dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/themes/semi-dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/core/menu/menu-types/vertical-menu.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/core/colors/palette-gradient.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/pages/dashboard-ecommerce.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/pages/card-analytics.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/custom-rtl.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style-rtl.css')); ?>">
    <?php else: ?>
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/vendors.min.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/bootstrap.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/bootstrap-extended.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/colors.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/components.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/themes/dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/themes/semi-dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/core/menu/menu-types/vertical-menu.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/core/colors/palette-gradient.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/pages/dashboard-ecommerce.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/pages/card-analytics.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/custom-rtl.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style_en.css')); ?>">
    <?php endif; ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style.css')); ?>">

    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/flatpickr.css')); ?>">

    <link rel="stylesheet" type="text/css" href="https://code.jquery.com/ui/1.12.0/themes/smoothness/jquery-ui.css">


    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/toastr.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/extensions/toastr.css')); ?>">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <?php echo $__env->yieldContent('css'); ?>
</head>
<!-- END: Head-->

<!-- BEGIN: Body-->


<body style="font-family: 'Cairo', sans-serif !important;" id="content_body" class="position-relative vertical-layout vertical-menu-modern 2-columns  navbar-floating footer-static" data-open="click" data-menu="vertical-menu-modern" data-col="2-columns">
    



        <?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/layout/partial/header.blade.php ENDPATH**/ ?>